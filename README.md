# LazyRemixer

A web application that randomly combines video files into creative remixes using AI-powered clip selection and server-side FFmpeg processing.

## Features

- **4-Step Workflow**: Select files → Configure settings → Generate videos → Download results
- **File System Access API**: Modern browser file handling with traditional fallback
- **Server-Side Processing**: FFmpeg video processing for better performance and stability
- **Real-Time Progress**: Server-Sent Events for live generation updates
- **Random Algorithm**: Fisher<PERSON><PERSON> shuffle for clip selection (0.5s-15s clips)
- **Auto Downloads**: Videos download individually as they complete
- **Dark Theme**: Modern black-on-black UI design
- **Docker Ready**: Complete containerization with health checks

## Supported Formats

- MP4, AVI, MOV, MKV, WebM

## Quick Start

### Using Docker (Recommended)

```bash
# Clone the repository
git clone <repository-url>
cd LazyRemixer

# Build and run with Docker Compose
docker-compose up --build

# Access the application
open http://localhost:3000
```

### Manual Installation

```bash
# Install dependencies
npm install

# Start the development server
npm run dev

# Or start production server
npm start
```

## Configuration

### Generation Settings

- **Clips per video**: 2-50 (default: 10)
- **Number of videos**: 1-100 (default: 1)
- **Clip duration**: Automatically uses random portions from 0.1 seconds to full video length
- **Splitter video**: Optional transition video file between clips

### Environment Variables

- `PORT`: Server port (default: 3000)
- `NODE_ENV`: Environment mode (development/production)

## API Endpoints

### Session Management
- `POST /api/session` - Create new session
- `DELETE /api/session/:sessionId` - Cleanup session

### File Upload
- `POST /api/upload/file` - Upload single file
- `POST /api/upload/files` - Upload multiple files
- `GET /api/upload/files/:sessionId` - List uploaded files
- `DELETE /api/upload/file/:sessionId/:fileId` - Delete uploaded file

### Video Generation
- `POST /api/generate/:sessionId` - Start video generation
- `GET /api/generate/status/:sessionId` - Get generation status
- `GET /api/progress/:sessionId` - Server-Sent Events for progress

### Downloads
- `GET /api/download/:sessionId/:filename` - Download video
- `GET /api/download/stream/:sessionId/:filename` - Stream video
- `GET /api/download/list/:sessionId` - List available downloads
- `DELETE /api/download/:sessionId/:filename` - Delete generated video

### Health Check
- `GET /health` - Server health status

## Architecture

### Backend Components
- **server.js**: Main Express server with SSE support
- **routes/upload.js**: File upload handling with multer
- **routes/generate.js**: Video generation with FFmpeg processing
- **routes/download.js**: File download and cleanup

### Frontend Components
- **fileManager.js**: File System Access API with fallback
- **videoProcessor.js**: Server communication and progress tracking
- **ui.js**: UI state management and navigation
- **app.js**: Main application controller

## Development

### Prerequisites
- Node.js 16+
- FFmpeg (automatically included in Docker)

### Scripts
- `npm start`: Start production server
- `npm run dev`: Start development server with nodemon
- `npm run docker:build`: Build Docker image
- `npm run docker:run`: Run Docker container
- `npm run docker:compose`: Run with Docker Compose

### File Structure
```
LazyRemixer/
├── server.js              # Main server
├── package.json           # Dependencies
├── Dockerfile             # Docker configuration
├── docker-compose.yml     # Docker Compose setup
├── routes/                # API routes
│   ├── upload.js         # File upload handling
│   ├── generate.js       # Video generation
│   └── download.js       # File downloads
├── public/               # Frontend files
│   ├── index.html       # Main HTML
│   ├── css/
│   │   └── styles.css   # Dark theme styles
│   └── js/
│       ├── app.js       # Main app controller
│       ├── fileManager.js # File handling
│       ├── videoProcessor.js # Video processing
│       └── ui.js        # UI management
├── uploads/             # Temporary upload storage
└── output/              # Generated video output
```

## Browser Support

- **File System Access API**: Chrome 86+, Edge 86+
- **Fallback**: All modern browsers with traditional file input
- **Server-Sent Events**: All modern browsers

## Performance

- Server-side FFmpeg processing for optimal performance
- Streaming downloads for large files
- Automatic cleanup of temporary files
- Resource limits in Docker configuration

## Security

- File type validation
- Session-based isolation
- Non-root Docker user
- Input sanitization

## Troubleshooting

### Common Issues

1. **FFmpeg not found**: Ensure FFmpeg is installed or use Docker
2. **File upload fails**: Check file format and size limits
3. **Generation stuck**: Check server logs for FFmpeg errors
4. **Connection lost**: Server-Sent Events will auto-reconnect

### Debug Mode

Access debug information in browser console:
```javascript
// Get application state
console.log(window.app.instance.getDebugInfo());

// Test server connection
window.app.instance.testServerConnection();
```

## License

MIT License - see LICENSE file for details.

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## Support

For issues and questions, please use the GitHub issue tracker.
