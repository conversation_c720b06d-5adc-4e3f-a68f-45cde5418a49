version: '3.8'

services:
  lazy-remixer:
    build: .
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - PORT=3000
      - NODE_OPTIONS=--max-old-space-size=3072
      - UV_THREADPOOL_SIZE=16
    volumes:
      # Persist uploads and output directories
      - uploads_data:/usr/src/app/uploads
      - output_data:/usr/src/app/output
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "node", "-e", "require('http').get('http://localhost:3000/health', (res) => { process.exit(res.statusCode === 200 ? 0 : 1) })"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    deploy:
      resources:
        limits:
          memory: 4G
          cpus: '2.0'
        reservations:
          memory: 1G
          cpus: '1.0'

volumes:
  uploads_data:
    driver: local
  output_data:
    driver: local
