{"name": "lazy-remixer", "version": "1.0.0", "description": "A web application that randomly combines video files into creative remixes", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "docker:build": "docker build -t lazy-remixer .", "docker:run": "docker run -p 3000:3000 lazy-remixer", "docker:compose": "docker-compose up --build"}, "keywords": ["video", "remix", "ffmpeg", "express", "file-processing"], "author": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "license": "MIT", "dependencies": {"adm-zip": "^0.5.16", "archiver": "^7.0.1", "cors": "^2.8.5", "express": "^4.18.2", "ffmpeg-static": "^5.2.0", "fluent-ffmpeg": "^2.1.2", "fs-extra": "^11.1.1", "multer": "^1.4.5-lts.1", "uuid": "^9.0.1"}, "devDependencies": {"nodemon": "^3.0.1"}, "engines": {"node": ">=16.0.0"}}