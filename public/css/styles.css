/* <PERSON><PERSON><PERSON><PERSON><PERSON>er Dark Theme Styles */

:root {
  /* Dark Theme Colors */
  --bg-primary: #0a0a0a;
  --bg-secondary: #1a1a1a;
  --bg-tertiary: #2a2a2a;
  --bg-hover: #3a3a3a;
  
  --text-primary: #ffffff;
  --text-secondary: #cccccc;
  --text-muted: #888888;
  
  --accent-primary: #4a9eff;
  --accent-secondary: #6b73ff;
  --accent-success: #00d084;
  --accent-warning: #ffb800;
  --accent-danger: #ff4757;
  
  --border-color: #333333;
  --border-hover: #555555;
  
  /* Spacing */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;
  
  /* Border Radius */
  --radius-sm: 4px;
  --radius-md: 8px;
  --radius-lg: 12px;
  
  /* Shadows */
  --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.3);
  --shadow-md: 0 4px 8px rgba(0, 0, 0, 0.4);
  --shadow-lg: 0 8px 16px rgba(0, 0, 0, 0.5);
}

/* Reset and Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
  background: var(--bg-primary);
  color: var(--text-primary);
  line-height: 1.6;
  min-height: 100vh;
}

/* App Container */
.app-container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* Header */
.app-header {
  background: var(--bg-secondary);
  border-bottom: 1px solid var(--border-color);
  padding: var(--spacing-lg) var(--spacing-xl);
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
}

.app-title {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: var(--spacing-sm);
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.title-icon {
  font-size: 2rem;
}

.app-subtitle {
  color: var(--text-secondary);
  font-size: 1.1rem;
}

/* Progress Steps */
.progress-container {
  background: var(--bg-secondary);
  border-bottom: 1px solid var(--border-color);
  padding: var(--spacing-lg) var(--spacing-xl);
}

.progress-steps {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  justify-content: space-between;
  position: relative;
}

.progress-steps::before {
  content: '';
  position: absolute;
  top: 20px;
  left: 40px;
  right: 40px;
  height: 2px;
  background: var(--border-color);
  z-index: 1;
}

.step {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  z-index: 2;
}

.step-number {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: var(--bg-tertiary);
  border: 2px solid var(--border-color);
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  margin-bottom: var(--spacing-sm);
  transition: all 0.3s ease;
}

.step.active .step-number {
  background: var(--accent-primary);
  border-color: var(--accent-primary);
  color: white;
}

.step.completed .step-number {
  background: var(--accent-success);
  border-color: var(--accent-success);
  color: white;
}

.step-label {
  font-size: 0.9rem;
  color: var(--text-muted);
  text-align: center;
}

.step.active .step-label {
  color: var(--text-primary);
  font-weight: 500;
}

/* Main Content */
.main-content {
  flex: 1;
  max-width: 1200px;
  margin: 0 auto;
  padding: var(--spacing-xl);
  width: 100%;
}

.step-content {
  display: none;
}

.step-content.active {
  display: block;
}

.step-header {
  text-align: center;
  margin-bottom: var(--spacing-xl);
}

.step-header h2 {
  font-size: 2rem;
  margin-bottom: var(--spacing-sm);
}

.step-header p {
  color: var(--text-secondary);
  font-size: 1.1rem;
}

/* File Selection */
.upload-area {
  border: 2px dashed var(--border-color);
  border-radius: var(--radius-lg);
  padding: var(--spacing-xl);
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-bottom: var(--spacing-lg);
}

.upload-area:hover,
.upload-area.dragover {
  border-color: var(--accent-primary);
  background: rgba(74, 158, 255, 0.05);
}

.upload-icon {
  font-size: 3rem;
  margin-bottom: var(--spacing-md);
}

.upload-text h3 {
  margin-bottom: var(--spacing-sm);
  color: var(--text-primary);
}

.upload-text p {
  color: var(--text-muted);
}

.file-system-access {
  text-align: center;
  margin-bottom: var(--spacing-lg);
}

.help-text {
  color: var(--text-muted);
  font-size: 0.9rem;
  margin-top: var(--spacing-sm);
}

/* Selected Files */
.selected-files {
  background: var(--bg-secondary);
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
  margin-bottom: var(--spacing-lg);
}

.files-list {
  max-height: 300px;
  overflow-y: auto;
  margin-bottom: var(--spacing-md);
}

.file-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-md);
  background: var(--bg-tertiary);
  border-radius: var(--radius-md);
  margin-bottom: var(--spacing-sm);
}

.file-info {
  display: flex;
  flex-direction: column;
  flex: 1;
}

.file-name {
  font-weight: 500;
  margin-bottom: var(--spacing-xs);
}

.file-details {
  font-size: 0.9rem;
  color: var(--text-muted);
}

.file-actions {
  display: flex;
  gap: var(--spacing-sm);
}

.files-summary {
  padding: var(--spacing-md);
  background: var(--bg-tertiary);
  border-radius: var(--radius-md);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* Settings Grid */
.settings-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-xl);
}

.setting-group {
  display: flex;
  flex-direction: column;
}

.setting-group.full-width {
  grid-column: 1 / -1;
}

.setting-group label {
  font-weight: 500;
  margin-bottom: var(--spacing-sm);
  color: var(--text-primary);
}

.setting-group input[type="number"] {
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  padding: var(--spacing-md);
  color: var(--text-primary);
  font-size: 1rem;
  transition: border-color 0.3s ease;
}

.setting-group input[type="number"]:focus {
  outline: none;
  border-color: var(--accent-primary);
}

.setting-group input[type="checkbox"] {
  margin-right: var(--spacing-sm);
}

.setting-group input[type="file"] {
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  padding: var(--spacing-md);
  color: var(--text-primary);
  font-size: 1rem;
  transition: border-color 0.3s ease;
  width: 100%;
}

.setting-group input[type="file"]:focus {
  outline: none;
  border-color: var(--accent-primary);
}

.splitter-video-info {
  background: var(--bg-tertiary);
  border-radius: var(--radius-md);
  padding: var(--spacing-md);
  margin-top: var(--spacing-sm);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.splitter-video-name {
  font-weight: 500;
  color: var(--text-primary);
}

.setting-help {
  font-size: 0.9rem;
  color: var(--text-muted);
  margin-top: var(--spacing-xs);
}

/* Progress Bars */
.generation-progress {
  margin-bottom: var(--spacing-xl);
}

.overall-progress,
.current-video-progress {
  margin-bottom: var(--spacing-lg);
}

.progress-label {
  font-weight: 500;
  margin-bottom: var(--spacing-sm);
}

.progress-bar {
  background: var(--bg-tertiary);
  border-radius: var(--radius-md);
  height: 12px;
  overflow: hidden;
  margin-bottom: var(--spacing-sm);
}

.progress-fill {
  background: linear-gradient(90deg, var(--accent-primary), var(--accent-secondary));
  height: 100%;
  width: 0%;
  transition: width 0.3s ease;
}

.progress-text {
  text-align: center;
  font-weight: 500;
  color: var(--text-secondary);
}

/* Upload Progress */
.upload-progress-container {
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
  margin: var(--spacing-lg) 0;
  display: none;
}

.upload-progress-header {
  margin-bottom: var(--spacing-lg);
}

.upload-progress-header h3 {
  color: var(--text-primary);
  margin-bottom: var(--spacing-md);
  font-size: 1.2rem;
}

.upload-overall-progress {
  margin-bottom: var(--spacing-md);
}

.upload-current-file {
  background: var(--bg-tertiary);
  border-radius: var(--radius-md);
  padding: var(--spacing-md);
  margin-bottom: var(--spacing-lg);
}

.current-file-label {
  font-weight: 500;
  color: var(--text-primary);
  margin-bottom: var(--spacing-sm);
  font-size: 0.9rem;
}

.upload-status-lists {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-lg);
}

.upload-successful,
.upload-failed {
  background: var(--bg-tertiary);
  border-radius: var(--radius-md);
  padding: var(--spacing-md);
}

.upload-successful h4,
.upload-failed h4 {
  font-size: 0.9rem;
  margin-bottom: var(--spacing-sm);
  color: var(--text-primary);
}

.upload-successful h4 {
  color: var(--accent-success);
}

.upload-failed h4 {
  color: var(--accent-danger);
}

.file-list {
  max-height: 150px;
  overflow-y: auto;
}

.file-list-item {
  font-size: 0.8rem;
  color: var(--text-secondary);
  padding: var(--spacing-xs) 0;
  border-bottom: 1px solid var(--border-color);
}

.file-list-item:last-child {
  border-bottom: none;
}

/* Generation Log */
.generation-log {
  background: var(--bg-secondary);
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
  max-height: 200px;
  overflow-y: auto;
  margin-bottom: var(--spacing-lg);
}

.log-entry {
  padding: var(--spacing-sm) 0;
  border-bottom: 1px solid var(--border-color);
  font-family: 'Courier New', monospace;
  font-size: 0.9rem;
}

.log-entry:last-child {
  border-bottom: none;
}

.log-entry.success {
  color: var(--accent-success);
}

.log-entry.error {
  color: var(--accent-danger);
}

.log-entry.warning {
  color: var(--accent-warning);
}

/* Buttons */
.btn {
  padding: var(--spacing-md) var(--spacing-lg);
  border: none;
  border-radius: var(--radius-md);
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.btn-primary {
  background: var(--accent-primary);
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background: #3a8ae6;
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.btn-secondary {
  background: var(--bg-tertiary);
  color: var(--text-primary);
  border: 1px solid var(--border-color);
}

.btn-secondary:hover:not(:disabled) {
  background: var(--bg-hover);
  border-color: var(--border-hover);
}

.btn-danger {
  background: var(--accent-danger);
  color: white;
}

.btn-danger:hover:not(:disabled) {
  background: #e63946;
}

.btn-success {
  background: var(--accent-success);
  color: white;
}

.btn-success:hover:not(:disabled) {
  background: #00b574;
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.btn-small {
  padding: var(--spacing-sm) var(--spacing-md);
  font-size: 0.9rem;
}

/* Step Actions */
.step-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: var(--spacing-xl);
}

/* Status Bar */
.status-bar {
  background: var(--bg-secondary);
  border-top: 1px solid var(--border-color);
  padding: var(--spacing-md) var(--spacing-xl);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.connection-status {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  font-size: 0.9rem;
  color: var(--text-muted);
}

.status-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: var(--accent-success);
}

.status-indicator.disconnected {
  background: var(--accent-danger);
}

/* Loading Overlay */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid var(--bg-tertiary);
  border-top: 4px solid var(--accent-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: var(--spacing-md);
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  color: var(--text-primary);
  font-size: 1.1rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .app-header,
  .progress-container,
  .main-content {
    padding-left: var(--spacing-md);
    padding-right: var(--spacing-md);
  }
  
  .app-title {
    font-size: 2rem;
  }
  
  .progress-steps {
    flex-direction: column;
    gap: var(--spacing-md);
  }
  
  .progress-steps::before {
    display: none;
  }
  
  .settings-grid {
    grid-template-columns: 1fr;
  }
  
  .step-actions {
    flex-direction: column;
    gap: var(--spacing-md);
  }
  
  .step-actions .btn {
    width: 100%;
    justify-content: center;
  }

  .upload-status-lists {
    grid-template-columns: 1fr;
    gap: var(--spacing-md);
  }

  .upload-progress-container {
    margin: var(--spacing-md) 0;
    padding: var(--spacing-md);
  }
}

/* Scrollbar Styling */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--bg-tertiary);
}

::-webkit-scrollbar-thumb {
  background: var(--border-color);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--border-hover);
}
