<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LazyRemixer - Video Remix Generator</title>
    <link rel="stylesheet" href="css/styles.css">
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🎬</text></svg>">
</head>
<body>
    <div class="app-container">
        <!-- Header -->
        <header class="app-header">
            <div class="header-content">
                <h1 class="app-title">
                    <span class="title-icon">🎬</span>
                    LazyRemixer
                </h1>
                <p class="app-subtitle">Create random video remixes with AI-powered clip selection</p>
            </div>
        </header>

        <!-- Progress Indicator -->
        <div class="progress-container">
            <div class="progress-steps">
                <div class="step active" data-step="1">
                    <div class="step-number">1</div>
                    <div class="step-label">Select Files</div>
                </div>
                <div class="step" data-step="2">
                    <div class="step-number">2</div>
                    <div class="step-label">Configure</div>
                </div>
                <div class="step" data-step="3">
                    <div class="step-number">3</div>
                    <div class="step-label">Generate</div>
                </div>
                <div class="step" data-step="4">
                    <div class="step-number">4</div>
                    <div class="step-label">Download</div>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Step 1: File Selection -->
            <section class="step-content active" id="step-1">
                <div class="step-header">
                    <h2>Select Video Files</h2>
                    <p>Choose video files to remix. Supported formats: MP4, AVI, MOV, MKV, WebM</p>
                </div>

                <div class="file-selection">
                    <div class="upload-area" id="upload-area">
                        <div class="upload-icon">📁</div>
                        <div class="upload-text">
                            <h3>Drop video files here or click to browse</h3>
                            <p>You can select multiple files at once</p>
                        </div>
                        <input type="file" id="file-input" multiple accept="video/*" style="display: none;">
                    </div>

                    <div class="file-system-access" id="file-system-access" style="display: none;">
                        <button class="btn btn-primary" id="select-directory-btn">
                            📂 Select Video Directory
                        </button>
                        <p class="help-text">Use File System Access API for better performance</p>
                    </div>
                </div>

                <div class="selected-files" id="selected-files" style="display: none;">
                    <h3>Selected Files</h3>
                    <div class="files-list" id="files-list"></div>
                    <div class="files-summary" id="files-summary"></div>
                </div>

                <div class="step-actions">
                    <button class="btn btn-primary" id="next-to-configure" disabled>
                        Next: Configure Settings →
                    </button>
                </div>
            </section>

            <!-- Step 2: Configuration -->
            <section class="step-content" id="step-2">
                <div class="step-header">
                    <h2>Configure Remix Settings</h2>
                    <p>Customize how your videos will be remixed</p>
                </div>

                <div class="settings-grid">
                    <div class="setting-group">
                        <label for="clips-per-video">Clips per Video</label>
                        <input type="number" id="clips-per-video" min="2" max="50" value="10">
                        <span class="setting-help">Number of random clips in each remix (2-50)</span>
                    </div>

                    <div class="setting-group">
                        <label for="number-of-videos">Number of Videos</label>
                        <input type="number" id="number-of-videos" min="1" max="100" value="1">
                        <span class="setting-help">How many remix videos to generate (1-100)</span>
                    </div>

                    <div class="setting-group full-width">
                        <label>
                            <input type="checkbox" id="use-splitter">
                            Use splitter video between clips
                        </label>
                        <span class="setting-help">Add a transition video between each clip</span>
                    </div>

                    <div class="setting-group full-width splitter-selection" id="splitter-selection" style="display: none;">
                        <label for="splitter-video-input">Select Splitter Video</label>
                        <input type="file" id="splitter-video-input" accept="video/*">
                        <span class="setting-help">Choose a video file to use as transition between clips</span>
                        <div class="splitter-video-info" id="splitter-video-info" style="display: none;">
                            <div class="splitter-video-name"></div>
                            <button type="button" class="btn btn-small btn-secondary" id="remove-splitter-video">Remove</button>
                        </div>
                    </div>
                </div>

                <div class="step-actions">
                    <button class="btn btn-secondary" id="back-to-files">
                        ← Back to Files
                    </button>
                    <button class="btn btn-primary" id="start-generation">
                        Start Generation →
                    </button>
                </div>
            </section>

            <!-- Step 3: Generation -->
            <section class="step-content" id="step-3">
                <div class="step-header">
                    <h2>Generating Remixes</h2>
                    <p>Your videos are being processed. This may take a few minutes...</p>
                </div>

                <div class="generation-progress">
                    <div class="overall-progress">
                        <div class="progress-label">Overall Progress</div>
                        <div class="progress-bar">
                            <div class="progress-fill" id="overall-progress"></div>
                        </div>
                        <div class="progress-text" id="overall-progress-text">0%</div>
                    </div>

                    <div class="current-video-progress" id="current-video-progress" style="display: none;">
                        <div class="progress-label">Current Video</div>
                        <div class="progress-bar">
                            <div class="progress-fill" id="video-progress"></div>
                        </div>
                        <div class="progress-text" id="video-progress-text">0%</div>
                    </div>

                    <div class="generation-log" id="generation-log">
                        <div class="log-entry">Initializing generation...</div>
                    </div>
                </div>

                <div class="completed-videos" id="completed-videos" style="display: none;">
                    <h3>Completed Videos</h3>
                    <div class="videos-list" id="completed-videos-list"></div>
                </div>

                <div class="step-actions">
                    <button class="btn btn-secondary" id="cancel-generation">
                        Cancel Generation
                    </button>
                    <button class="btn btn-primary" id="go-to-downloads" style="display: none;">
                        View Downloads →
                    </button>
                </div>
            </section>

            <!-- Step 4: Downloads -->
            <section class="step-content" id="step-4">
                <div class="step-header">
                    <h2>Download Your Remixes</h2>
                    <p>Your remixed videos are ready for download</p>
                </div>

                <div class="downloads-container">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                        <h3>Available Downloads</h3>
                        <div style="display: flex; gap: 10px;">
                            <button class="btn btn-small btn-success" id="download-all-zip" style="display: none;">
                                📦 Download All as ZIP
                            </button>
                            <button class="btn btn-small btn-secondary" id="debug-output">
                                🔍 Debug Output
                            </button>
                            <button class="btn btn-small btn-primary" id="refresh-downloads">
                                🔄 Refresh Downloads
                            </button>
                        </div>
                    </div>
                    <div class="downloads-list" id="downloads-list"></div>
                </div>

                <div class="step-actions">
                    <button class="btn btn-secondary" id="start-new-session">
                        🔄 Start New Session
                    </button>
                    <button class="btn btn-danger" id="cleanup-files">
                        🗑️ Cleanup Files
                    </button>
                </div>
            </section>
        </main>

        <!-- Status Bar -->
        <div class="status-bar" id="status-bar">
            <div class="status-text" id="status-text">Ready</div>
            <div class="connection-status" id="connection-status">
                <span class="status-indicator"></span>
                <span>Connected</span>
            </div>
        </div>
    </div>

    <!-- Loading Overlay -->
    <div class="loading-overlay" id="loading-overlay" style="display: none;">
        <div class="loading-spinner"></div>
        <div class="loading-text">Processing...</div>
    </div>

    <!-- Scripts -->
    <script src="js/fileManager.js"></script>
    <script src="js/videoProcessor.js"></script>
    <script src="js/ui.js"></script>
    <script src="js/app.js"></script>
</body>
</html>
