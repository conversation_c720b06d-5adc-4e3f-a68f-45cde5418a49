// Main Application Controller
class LazyRemixerApp {
  constructor() {
    this.initialized = false;
    this.init();
  }

  async init() {
    try {
      console.log('Initializing LazyRemixer...');
      
      // Setup global error handling
      this.setupErrorHandling();
      
      // Setup file manager callbacks
      this.setupFileManagerCallbacks();
      
      // Setup video processor callbacks
      this.setupVideoProcessorCallbacks();
      
      // Check server connection
      await this.checkServerConnection();
      
      // Setup periodic health checks
      this.setupHealthChecks();
      
      // Setup cleanup on page unload
      this.setupCleanup();
      
      this.initialized = true;
      console.log('LazyRemixer initialized successfully');
      
      uiManager.showStatus('Application ready', 'success');
      
    } catch (error) {
      console.error('Initialization error:', error);
      uiManager.showStatus('Failed to initialize application', 'error');
    }
  }

  setupErrorHandling() {
    // Global error handler
    window.addEventListener('error', (event) => {
      console.error('Global error:', event.error);
      uiManager.showStatus('An unexpected error occurred', 'error');
    });

    // Unhandled promise rejection handler
    window.addEventListener('unhandledrejection', (event) => {
      console.error('Unhandled promise rejection:', event.reason);
      uiManager.showStatus('An unexpected error occurred', 'error');
      event.preventDefault();
    });
  }

  setupFileManagerCallbacks() {
    // Update UI when files change
    fileManager.onFilesChanged = (files) => {
      console.log('Files changed:', files.length);
      uiManager.updateNavigationButtons();
      
      if (files.length > 0) {
        uiManager.showStatus(`${files.length} files selected`, 'success');
      }
    };

    // Handle upload progress
    fileManager.onUploadProgress = (progress) => {
      console.log('Upload progress:', progress);

      // Update status bar with current upload progress
      if (progress.current) {
        uiManager.showStatus(
          `Uploading file ${progress.current.index} of ${progress.total}: ${progress.current.name} (${progress.overallPercent}% overall)`,
          'info'
        );
      } else if (progress.completed > 0 || progress.failed > 0) {
        uiManager.showStatus(
          `Upload progress: ${progress.completed + progress.failed}/${progress.total} files processed (${progress.overallPercent}%)`,
          'info'
        );
      }
    };
  }

  setupVideoProcessorCallbacks() {
    // These are already handled in uiManager.setupProgressCallbacks()
    // but we can add additional app-level handling here if needed
    
    videoProcessor.onGenerationCompleted = (data) => {
      console.log('Generation completed:', data);
      uiManager.handleGenerationCompleted(data);
      
      // Show notification if supported
      this.showNotification('Video generation completed!', 'Your remixed videos are ready for download.');
    };

    videoProcessor.onError = (error) => {
      console.error('Video processing error:', error);
      uiManager.handleGenerationError(error);
      
      // Show error notification
      this.showNotification('Generation failed', error.message, 'error');
    };
  }

  async checkServerConnection() {
    try {
      const response = await fetch('/health');
      
      if (!response.ok) {
        throw new Error('Server health check failed');
      }
      
      const health = await response.json();
      console.log('Server health:', health);
      
      uiManager.updateConnectionStatus(true);
      return true;
      
    } catch (error) {
      console.error('Server connection error:', error);
      uiManager.updateConnectionStatus(false);
      uiManager.showStatus('Server connection failed', 'error');
      return false;
    }
  }

  setupHealthChecks() {
    // Check server health every 30 seconds
    setInterval(async () => {
      const connected = await this.checkServerConnection();
      
      if (!connected && this.initialized) {
        uiManager.showStatus('Lost connection to server', 'warning');
      }
    }, 30000);
  }

  setupCleanup() {
    // Cleanup on page unload
    window.addEventListener('beforeunload', async (event) => {
      try {
        // Cleanup file manager session
        await fileManager.cleanup();
        
        // Cleanup video processor
        videoProcessor.cleanup();
        
      } catch (error) {
        console.error('Cleanup error:', error);
      }
    });

    // Cleanup on visibility change (when tab becomes hidden)
    document.addEventListener('visibilitychange', () => {
      if (document.hidden) {
        console.log('Tab hidden, maintaining connections...');
      } else {
        console.log('Tab visible, checking connections...');
        this.checkServerConnection();
      }
    });
  }

  showNotification(title, body, type = 'info') {
    // Check if notifications are supported and permitted
    if ('Notification' in window && Notification.permission === 'granted') {
      const notification = new Notification(title, {
        body: body,
        icon: '/favicon.ico',
        tag: 'lazy-remixer'
      });

      // Auto-close after 5 seconds
      setTimeout(() => {
        notification.close();
      }, 5000);
      
    } else if ('Notification' in window && Notification.permission !== 'denied') {
      // Request permission
      Notification.requestPermission().then(permission => {
        if (permission === 'granted') {
          this.showNotification(title, body, type);
        }
      });
    }
  }

  // Utility methods for other parts of the app
  async downloadAllVideos() {
    try {
      const downloads = await videoProcessor.getDownloadsList();
      
      if (downloads.files.length === 0) {
        uiManager.showStatus('No videos to download', 'warning');
        return;
      }

      uiManager.showStatus(`Starting download of ${downloads.files.length} videos...`, 'info');
      
      // Download each video with a small delay to avoid overwhelming the browser
      for (const file of downloads.files) {
        await videoProcessor.downloadVideo(file.filename);
        await new Promise(resolve => setTimeout(resolve, 1000)); // 1 second delay
      }
      
      uiManager.showStatus('All downloads started', 'success');
      
    } catch (error) {
      console.error('Batch download error:', error);
      uiManager.showStatus('Batch download failed', 'error');
    }
  }

  async exportSettings() {
    const settings = uiManager.getGenerationSettings();
    const files = fileManager.getSelectedFiles();
    
    const exportData = {
      version: '1.0',
      timestamp: new Date().toISOString(),
      settings: settings,
      files: files.map(f => ({
        name: f.name,
        size: f.size,
        duration: f.duration
      }))
    };

    const blob = new Blob([JSON.stringify(exportData, null, 2)], {
      type: 'application/json'
    });
    
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `lazy-remixer-settings-${Date.now()}.json`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
    
    uiManager.showStatus('Settings exported', 'success');
  }

  async importSettings(file) {
    try {
      const text = await file.text();
      const data = JSON.parse(text);
      
      if (data.version && data.settings) {
        // Apply settings to UI
        const settings = data.settings;
        
        if (settings.clipsPerVideo) {
          document.getElementById('clips-per-video').value = settings.clipsPerVideo;
        }
        if (settings.numberOfVideos) {
          document.getElementById('number-of-videos').value = settings.numberOfVideos;
        }
        if (settings.useSplitterVideo !== undefined) {
          document.getElementById('use-splitter').checked = settings.useSplitterVideo;
        }
        
        uiManager.showStatus('Settings imported successfully', 'success');
      } else {
        throw new Error('Invalid settings file format');
      }
      
    } catch (error) {
      console.error('Import settings error:', error);
      uiManager.showStatus('Failed to import settings', 'error');
    }
  }

  // Debug methods
  getDebugInfo() {
    return {
      initialized: this.initialized,
      currentStep: uiManager.currentStep,
      selectedFiles: fileManager.getSelectedFiles().length,
      sessionId: fileManager.getSessionId(),
      isGenerating: videoProcessor.isProcessing(),
      completedVideos: videoProcessor.getCompletedVideos().length
    };
  }

  async testServerConnection() {
    console.log('Testing server connection...');
    const connected = await this.checkServerConnection();
    console.log('Connection test result:', connected);
    return connected;
  }
}

// Initialize the application when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  window.lazyRemixerApp = new LazyRemixerApp();
});

// Make app available globally for debugging
window.app = {
  fileManager,
  videoProcessor,
  uiManager,
  get instance() {
    return window.lazyRemixerApp;
  }
};
