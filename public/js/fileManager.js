// File Manager with File System Access API and fallback
class FileManager {
  constructor() {
    this.selectedFiles = [];
    this.sessionId = null;
    this.supportsFileSystemAccess = 'showDirectoryPicker' in window;
    this.onFilesChanged = null;
    this.onUploadProgress = null;
    this.maxBatchSize = 10; // Default batch size
    this.maxFilesPerRequest = 50; // Server limit

    this.initializeElements();
    this.setupEventListeners();
  }

  initializeElements() {
    this.uploadArea = document.getElementById('upload-area');
    this.fileInput = document.getElementById('file-input');
    this.fileSystemAccessDiv = document.getElementById('file-system-access');
    this.selectDirectoryBtn = document.getElementById('select-directory-btn');
    this.selectedFilesDiv = document.getElementById('selected-files');
    this.filesList = document.getElementById('files-list');
    this.filesSummary = document.getElementById('files-summary');

    // Show File System Access button if supported
    if (this.supportsFileSystemAccess) {
      this.fileSystemAccessDiv.style.display = 'block';
    }
  }

  setupEventListeners() {
    // Traditional file input
    this.uploadArea.addEventListener('click', () => {
      this.fileInput.click();
    });

    this.fileInput.addEventListener('change', (e) => {
      this.handleFileSelection(Array.from(e.target.files));
    });

    // Drag and drop
    this.uploadArea.addEventListener('dragover', (e) => {
      e.preventDefault();
      this.uploadArea.classList.add('dragover');
    });

    this.uploadArea.addEventListener('dragleave', () => {
      this.uploadArea.classList.remove('dragover');
    });

    this.uploadArea.addEventListener('drop', (e) => {
      e.preventDefault();
      this.uploadArea.classList.remove('dragover');
      
      const files = Array.from(e.dataTransfer.files).filter(file => 
        file.type.startsWith('video/') || this.isVideoFile(file.name)
      );
      
      if (files.length > 0) {
        this.handleFileSelection(files);
      }
    });

    // File System Access API
    if (this.selectDirectoryBtn) {
      this.selectDirectoryBtn.addEventListener('click', () => {
        this.selectDirectory();
      });
    }
  }

  isVideoFile(filename) {
    const videoExtensions = ['.mp4', '.avi', '.mov', '.mkv', '.webm'];
    const ext = filename.toLowerCase().substring(filename.lastIndexOf('.'));
    return videoExtensions.includes(ext);
  }

  async selectDirectory() {
    try {
      if (!this.supportsFileSystemAccess) {
        throw new Error('File System Access API not supported');
      }

      const directoryHandle = await window.showDirectoryPicker();
      const files = [];

      for await (const [name, handle] of directoryHandle.entries()) {
        if (handle.kind === 'file' && this.isVideoFile(name)) {
          const file = await handle.getFile();
          files.push(file);
        }
      }

      if (files.length === 0) {
        this.showStatus('No video files found in selected directory', 'warning');
        return;
      }

      this.handleFileSelection(files);
    } catch (error) {
      if (error.name !== 'AbortError') {
        console.error('Directory selection error:', error);
        this.showStatus('Failed to access directory', 'error');
      }
    }
  }

  async handleFileSelection(files) {
    if (files.length === 0) return;

    // Validate file types
    const validFiles = files.filter(file => 
      file.type.startsWith('video/') || this.isVideoFile(file.name)
    );

    if (validFiles.length === 0) {
      this.showStatus('No valid video files selected', 'warning');
      return;
    }

    if (validFiles.length !== files.length) {
      this.showStatus(`${files.length - validFiles.length} non-video files were ignored`, 'warning');
    }

    // Create session if needed
    if (!this.sessionId) {
      await this.createSession();
    }

    // Upload files
    this.showStatus('Uploading files...', 'info');
    await this.uploadFiles(validFiles);
  }

  async createSession() {
    try {
      const response = await fetch('/api/session', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error('Failed to create session');
      }

      const data = await response.json();
      this.sessionId = data.sessionId;
      console.log('Session created:', this.sessionId);
    } catch (error) {
      console.error('Session creation error:', error);
      throw error;
    }
  }

  async uploadFiles(files, retryCount = 0) {
    const maxRetries = 2;

    console.log(`📦 [UPLOAD] Starting sequential upload of ${files.length} files`);

    // Clear any existing selected files since we're starting a new upload
    this.selectedFiles = [];
    this.updateFilesDisplay();

    // Initialize upload progress tracking
    this.uploadProgress = {
      total: files.length,
      completed: 0,
      failed: 0,
      current: null,
      successfulFiles: [],
      failedFiles: []
    };

    // Show upload progress UI
    this.showUploadProgress();
    console.log('📊 [UPLOAD] Upload progress UI initialized');

    let totalUploaded = 0;

    for (let fileIndex = 0; fileIndex < files.length; fileIndex++) {
      const file = files[fileIndex];
      const fileNumber = fileIndex + 1;

      console.log(`📄 [UPLOAD] Processing file ${fileNumber}/${files.length}: ${file.name}`);

      // Update current file being uploaded
      this.uploadProgress.current = {
        name: file.name,
        index: fileNumber,
        progress: 0
      };
      this.updateUploadProgress();

      try {
        await this.uploadSingleFile(file, fileNumber, files.length, retryCount);

        // Mark file as successful
        this.uploadProgress.completed++;
        this.uploadProgress.successfulFiles.push({
          name: file.name,
          index: fileNumber
        });
        totalUploaded++;

        console.log(`✅ [UPLOAD] Successfully uploaded: ${file.name} (${totalUploaded}/${files.length})`);

        // Update progress
        this.uploadProgress.current.progress = 100;
        this.updateUploadProgress();

        // Small delay between files to prevent overwhelming the server
        if (fileIndex < files.length - 1) {
          await new Promise(resolve => setTimeout(resolve, 500));
        }

      } catch (error) {
        console.error(`❌ [UPLOAD] File ${fileNumber} failed: ${file.name}`, error);

        // Mark file as failed but continue with others
        this.uploadProgress.failed++;
        this.uploadProgress.failedFiles.push({
          name: file.name,
          index: fileNumber,
          error: error.message
        });

        // Update progress to show failure
        this.uploadProgress.current.progress = 100;
        this.updateUploadProgress();

        // If this is a 502 error and we haven't exceeded retries, retry this file
        if (error.message.includes('502') && retryCount < maxRetries) {
          console.log(`🔄 [UPLOAD] Retrying file due to 502 error: ${file.name}`);
          await new Promise(resolve => setTimeout(resolve, 2000));

          try {
            await this.uploadSingleFile(file, fileNumber, files.length, retryCount + 1);
            // If retry succeeds, update counters
            this.uploadProgress.failed--;
            this.uploadProgress.completed++;
            this.uploadProgress.failedFiles.pop(); // Remove from failed list
            this.uploadProgress.successfulFiles.push({
              name: file.name,
              index: fileNumber
            });
            totalUploaded++;
            console.log(`✅ [UPLOAD] Retry successful: ${file.name}`);
          } catch (retryError) {
            console.error(`❌ [UPLOAD] Retry failed for: ${file.name}`, retryError);
            // Keep in failed list, already added above
          }
        }

        // Continue with next file regardless of this file's failure
      }
    }

    // Hide upload progress and show final status
    this.hideUploadProgress();

    if (totalUploaded === files.length) {
      this.showStatus(`✅ Successfully uploaded all ${totalUploaded} files!`, 'success');
    } else if (totalUploaded > 0) {
      this.showStatus(`⚠️ Uploaded ${totalUploaded} of ${files.length} files. ${files.length - totalUploaded} files failed.`, 'warning');
    } else {
      this.showStatus(`❌ Failed to upload any files. Please check the files and try again.`, 'error');
    }

    // Log summary
    console.log(`📊 [UPLOAD] Summary: ${totalUploaded} successful, ${files.length - totalUploaded} failed`);
    if (this.uploadProgress.failedFiles.length > 0) {
      console.log('Failed files:', this.uploadProgress.failedFiles.map(f => f.name));
    }
  }

  async uploadSingleFile(file, fileNumber, totalFiles, retryCount = 0) {
    const maxRetries = 2;

    const formData = new FormData();
    formData.append('sessionId', this.sessionId);
    formData.append('video', file);

    try {
      console.log(`📤 [UPLOAD] Uploading file ${fileNumber}/${totalFiles}: ${file.name}`);
      console.log('Session ID:', this.sessionId);

      if (retryCount > 0) {
        console.log(`Retry attempt ${retryCount}/${maxRetries} for file: ${file.name}`);
        this.showStatus(`Retrying file ${fileNumber} (${retryCount}/${maxRetries}): ${file.name}`, 'warning');
      }

      const response = await fetch('/api/upload/file', {
        method: 'POST',
        body: formData
      });

      console.log('Upload response status:', response.status);

      // Handle 502 Bad Gateway specifically
      if (response.status === 502) {
        throw new Error('Server is temporarily unavailable (502 Bad Gateway). Please try again later.');
      }

      // Check if response is JSON
      const contentType = response.headers.get('content-type');
      if (!contentType || !contentType.includes('application/json')) {
        const text = await response.text();
        console.error('Non-JSON response:', text.substring(0, 500));

        if (response.status >= 500) {
          throw new Error(`Server error (${response.status}). Please try again later.`);
        } else {
          throw new Error(`Server returned non-JSON response (${response.status}). Check server logs.`);
        }
      }

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Upload failed');
      }

      const result = await response.json();
      console.log(`✅ [UPLOAD] File ${fileNumber} result:`, result);

      // Update selected files list
      if (result.file) {
        this.selectedFiles.push(result.file);
        this.updateFilesDisplay();

        if (this.onFilesChanged) {
          this.onFilesChanged(this.selectedFiles);
        }
      }

    } catch (error) {
      console.error(`❌ [UPLOAD] File ${fileNumber} error:`, error);
      throw error;
    }
  }

  updateFilesDisplay() {
    if (this.selectedFiles.length === 0) {
      this.selectedFilesDiv.style.display = 'none';
      return;
    }

    this.selectedFilesDiv.style.display = 'block';
    
    // Update files list
    this.filesList.innerHTML = '';
    this.selectedFiles.forEach(file => {
      const fileItem = this.createFileItem(file);
      this.filesList.appendChild(fileItem);
    });

    // Update summary
    const totalSize = this.selectedFiles.reduce((sum, file) => sum + file.size, 0);
    const totalDuration = this.selectedFiles.reduce((sum, file) => sum + (file.duration || 0), 0);
    
    this.filesSummary.innerHTML = `
      <div>
        <strong>${this.selectedFiles.length}</strong> files selected
      </div>
      <div>
        Total size: <strong>${this.formatFileSize(totalSize)}</strong>
      </div>
      <div>
        Total duration: <strong>${this.formatDuration(totalDuration)}</strong>
      </div>
    `;
  }

  createFileItem(file) {
    const item = document.createElement('div');
    item.className = 'file-item';
    
    item.innerHTML = `
      <div class="file-info">
        <div class="file-name">${file.name}</div>
        <div class="file-details">
          ${this.formatFileSize(file.size)} • ${this.formatDuration(file.duration || 0)}
        </div>
      </div>
      <div class="file-actions">
        <button class="btn btn-small btn-danger" onclick="fileManager.removeFile('${file.id}')">
          Remove
        </button>
      </div>
    `;
    
    return item;
  }

  async removeFile(fileId) {
    try {
      const response = await fetch(`/api/upload/file/${this.sessionId}/${fileId}`, {
        method: 'DELETE'
      });

      if (!response.ok) {
        throw new Error('Failed to remove file');
      }

      // Remove from local list
      this.selectedFiles = this.selectedFiles.filter(file => file.id !== fileId);
      this.updateFilesDisplay();
      
      if (this.onFilesChanged) {
        this.onFilesChanged(this.selectedFiles);
      }

      this.showStatus('File removed successfully', 'success');
    } catch (error) {
      console.error('Remove file error:', error);
      this.showStatus('Failed to remove file', 'error');
    }
  }

  formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  formatDuration(seconds) {
    if (!seconds || seconds === 0) return '0:00';
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  }

  showStatus(message, type = 'info') {
    const statusText = document.getElementById('status-text');
    if (statusText) {
      statusText.textContent = message;
      statusText.className = `status-text ${type}`;
      
      // Clear status after 5 seconds
      setTimeout(() => {
        statusText.textContent = 'Ready';
        statusText.className = 'status-text';
      }, 5000);
    }
  }

  getSessionId() {
    return this.sessionId;
  }

  getSelectedFiles() {
    return this.selectedFiles;
  }

  showUploadProgress() {
    try {
      // Create upload progress UI if it doesn't exist
      let uploadProgressDiv = document.getElementById('upload-progress');
      if (!uploadProgressDiv) {
        uploadProgressDiv = document.createElement('div');
        uploadProgressDiv.id = 'upload-progress';
        uploadProgressDiv.className = 'upload-progress-container';
        uploadProgressDiv.innerHTML = `
          <div class="upload-progress-header">
            <h3>Uploading Files</h3>
            <div class="upload-overall-progress">
              <div class="progress-label">Overall Progress</div>
              <div class="progress-bar">
                <div class="progress-fill" id="upload-overall-progress"></div>
              </div>
              <div class="progress-text" id="upload-overall-progress-text">0%</div>
            </div>
          </div>
          <div class="upload-current-file">
            <div class="current-file-label" id="current-file-label">Preparing...</div>
            <div class="progress-bar">
              <div class="progress-fill" id="upload-current-progress"></div>
            </div>
            <div class="progress-text" id="upload-current-progress-text">0%</div>
          </div>
          <div class="upload-status-lists">
            <div class="upload-successful">
              <h4>✅ Successful (<span id="successful-count">0</span>)</h4>
              <div class="file-list" id="successful-files-list"></div>
            </div>
            <div class="upload-failed">
              <h4>❌ Failed (<span id="failed-count">0</span>)</h4>
              <div class="file-list" id="failed-files-list"></div>
            </div>
          </div>
        `;

        // Insert after the file selection area
        const fileSelectionSection = document.querySelector('.file-selection');
        if (fileSelectionSection) {
          fileSelectionSection.parentNode.insertBefore(uploadProgressDiv, fileSelectionSection.nextSibling);
        } else {
          // Fallback: append to main content
          const mainContent = document.querySelector('.main-content');
          if (mainContent) {
            mainContent.appendChild(uploadProgressDiv);
          }
        }
      }

      uploadProgressDiv.style.display = 'block';
    } catch (error) {
      console.error('Error showing upload progress UI:', error);
    }
  }

  updateUploadProgress() {
    if (!this.uploadProgress) return;

    try {
      const { total, completed, failed, current } = this.uploadProgress;

    // Update overall progress
    const overallPercent = ((completed + failed) / total) * 100;
    const overallProgressBar = document.getElementById('upload-overall-progress');
    const overallProgressText = document.getElementById('upload-overall-progress-text');

    if (overallProgressBar) {
      overallProgressBar.style.width = `${overallPercent}%`;
    }
    if (overallProgressText) {
      overallProgressText.textContent = `${Math.round(overallPercent)}% (${completed + failed}/${total})`;
    }

    // Update current file progress
    const currentFileLabel = document.getElementById('current-file-label');
    const currentProgressBar = document.getElementById('upload-current-progress');
    const currentProgressText = document.getElementById('upload-current-progress-text');

    if (current) {
      if (currentFileLabel) {
        currentFileLabel.textContent = `Uploading file ${current.index} of ${total}: ${current.name}`;
      }
      if (currentProgressBar) {
        currentProgressBar.style.width = `${current.progress}%`;
      }
      if (currentProgressText) {
        currentProgressText.textContent = `${current.progress}%`;
      }
    }

    // Update successful files list
    const successfulCount = document.getElementById('successful-count');
    const successfulList = document.getElementById('successful-files-list');
    if (successfulCount) {
      successfulCount.textContent = completed;
    }
    if (successfulList) {
      successfulList.innerHTML = this.uploadProgress.successfulFiles
        .map(file => `<div class="file-list-item">✅ ${file.name}</div>`)
        .join('');
    }

    // Update failed files list
    const failedCount = document.getElementById('failed-count');
    const failedList = document.getElementById('failed-files-list');
    if (failedCount) {
      failedCount.textContent = failed;
    }
    if (failedList) {
      failedList.innerHTML = this.uploadProgress.failedFiles
        .map(file => `<div class="file-list-item">❌ ${file.name} (${file.error})</div>`)
        .join('');
    }

      // Call the progress callback if available
      if (this.onUploadProgress) {
        this.onUploadProgress({
          total,
          completed,
          failed,
          current,
          overallPercent: Math.round(overallPercent)
        });
      }
    } catch (error) {
      console.error('Error updating upload progress:', error);
    }
  }

  hideUploadProgress() {
    try {
      const uploadProgressDiv = document.getElementById('upload-progress');
      if (uploadProgressDiv) {
        // Keep it visible for a moment to show final results
        setTimeout(() => {
          uploadProgressDiv.style.display = 'none';
        }, 3000);
      }
    } catch (error) {
      console.error('Error hiding upload progress:', error);
    }
  }

  async cleanup() {
    if (this.sessionId) {
      try {
        await fetch(`/api/session/${this.sessionId}`, {
          method: 'DELETE'
        });
      } catch (error) {
        console.error('Cleanup error:', error);
      }
    }
  }
}

// Global instance
const fileManager = new FileManager();
