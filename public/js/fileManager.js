// File Manager with File System Access API and fallback
class FileManager {
  constructor() {
    this.selectedFiles = [];
    this.sessionId = null;
    this.supportsFileSystemAccess = 'showDirectoryPicker' in window;
    this.onFilesChanged = null;
    this.onUploadProgress = null;
    this.maxBatchSize = 10; // Default batch size
    this.maxFilesPerRequest = 50; // Server limit

    this.initializeElements();
    this.setupEventListeners();
  }

  initializeElements() {
    this.uploadArea = document.getElementById('upload-area');
    this.fileInput = document.getElementById('file-input');
    this.fileSystemAccessDiv = document.getElementById('file-system-access');
    this.selectDirectoryBtn = document.getElementById('select-directory-btn');
    this.selectedFilesDiv = document.getElementById('selected-files');
    this.filesList = document.getElementById('files-list');
    this.filesSummary = document.getElementById('files-summary');

    // Show File System Access button if supported
    if (this.supportsFileSystemAccess) {
      this.fileSystemAccessDiv.style.display = 'block';
    }
  }

  setupEventListeners() {
    // Traditional file input
    this.uploadArea.addEventListener('click', () => {
      this.fileInput.click();
    });

    this.fileInput.addEventListener('change', (e) => {
      this.handleFileSelection(Array.from(e.target.files));
    });

    // Drag and drop
    this.uploadArea.addEventListener('dragover', (e) => {
      e.preventDefault();
      this.uploadArea.classList.add('dragover');
    });

    this.uploadArea.addEventListener('dragleave', () => {
      this.uploadArea.classList.remove('dragover');
    });

    this.uploadArea.addEventListener('drop', (e) => {
      e.preventDefault();
      this.uploadArea.classList.remove('dragover');
      
      const files = Array.from(e.dataTransfer.files).filter(file => 
        file.type.startsWith('video/') || this.isVideoFile(file.name)
      );
      
      if (files.length > 0) {
        this.handleFileSelection(files);
      }
    });

    // File System Access API
    if (this.selectDirectoryBtn) {
      this.selectDirectoryBtn.addEventListener('click', () => {
        this.selectDirectory();
      });
    }
  }

  isVideoFile(filename) {
    const videoExtensions = ['.mp4', '.avi', '.mov', '.mkv', '.webm'];
    const ext = filename.toLowerCase().substring(filename.lastIndexOf('.'));
    return videoExtensions.includes(ext);
  }

  async selectDirectory() {
    try {
      if (!this.supportsFileSystemAccess) {
        throw new Error('File System Access API not supported');
      }

      const directoryHandle = await window.showDirectoryPicker();
      const files = [];

      for await (const [name, handle] of directoryHandle.entries()) {
        if (handle.kind === 'file' && this.isVideoFile(name)) {
          const file = await handle.getFile();
          files.push(file);
        }
      }

      if (files.length === 0) {
        this.showStatus('No video files found in selected directory', 'warning');
        return;
      }

      this.handleFileSelection(files);
    } catch (error) {
      if (error.name !== 'AbortError') {
        console.error('Directory selection error:', error);
        this.showStatus('Failed to access directory', 'error');
      }
    }
  }

  async handleFileSelection(files) {
    if (files.length === 0) return;

    // Validate file types
    const validFiles = files.filter(file => 
      file.type.startsWith('video/') || this.isVideoFile(file.name)
    );

    if (validFiles.length === 0) {
      this.showStatus('No valid video files selected', 'warning');
      return;
    }

    if (validFiles.length !== files.length) {
      this.showStatus(`${files.length - validFiles.length} non-video files were ignored`, 'warning');
    }

    // Create session if needed
    if (!this.sessionId) {
      await this.createSession();
    }

    // Upload files
    this.showStatus('Uploading files...', 'info');
    await this.uploadFiles(validFiles);
  }

  async createSession() {
    try {
      const response = await fetch('/api/session', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error('Failed to create session');
      }

      const data = await response.json();
      this.sessionId = data.sessionId;
      console.log('Session created:', this.sessionId);
    } catch (error) {
      console.error('Session creation error:', error);
      throw error;
    }
  }

  async uploadFiles(files, retryCount = 0) {
    const maxRetries = 2;

    // Adaptive batch sizing based on file count
    let batchSize;
    if (files.length <= 10) {
      batchSize = files.length; // Upload all at once if 10 or fewer
    } else if (files.length <= 30) {
      batchSize = 10; // Medium batches for moderate file counts
    } else {
      batchSize = 8; // Smaller batches for large file counts
    }

    // Ensure we don't exceed server limits
    batchSize = Math.min(batchSize, this.maxFilesPerRequest);

    console.log(`📦 [UPLOAD] Starting adaptive batch upload of ${files.length} files in batches of ${batchSize}`);

    // Split files into batches
    const batches = [];
    for (let i = 0; i < files.length; i += batchSize) {
      batches.push(files.slice(i, i + batchSize));
    }

    console.log(`📦 [UPLOAD] Created ${batches.length} batches`);

    let totalUploaded = 0;

    for (let batchIndex = 0; batchIndex < batches.length; batchIndex++) {
      const batch = batches[batchIndex];
      console.log(`📦 [UPLOAD] Processing batch ${batchIndex + 1}/${batches.length} with ${batch.length} files`);

      try {
        await this.uploadBatch(batch, batchIndex + 1, batches.length, retryCount);
        totalUploaded += batch.length;
        this.showStatus(`Uploaded batch ${batchIndex + 1}/${batches.length} (${totalUploaded}/${files.length} files)`, 'info');

        // Small delay between batches to prevent overwhelming the server
        if (batchIndex < batches.length - 1) {
          await new Promise(resolve => setTimeout(resolve, 1000));
        }
      } catch (error) {
        console.error(`❌ [UPLOAD] Batch ${batchIndex + 1} failed:`, error);

        // If this is a "too many files" error, try with smaller batches
        if (error.message.includes('Too many files') && batchSize > 1) {
          console.log(`🔄 [UPLOAD] Reducing batch size and retrying...`);
          this.maxBatchSize = Math.max(1, Math.floor(batchSize / 2));
          await new Promise(resolve => setTimeout(resolve, 1000));
          return this.uploadFiles(files, retryCount);
        }

        // If this is a 502 error and we haven't exceeded retries, retry the whole upload
        if (error.message.includes('502') && retryCount < maxRetries) {
          console.log(`🔄 [UPLOAD] Retrying entire upload due to 502 error...`);
          await new Promise(resolve => setTimeout(resolve, 3000));
          return this.uploadFiles(files, retryCount + 1);
        }

        throw error;
      }
    }

    this.showStatus(`✅ Successfully uploaded all ${totalUploaded} files!`, 'success');
  }

  async uploadBatch(files, batchNumber, totalBatches, retryCount = 0) {
    const maxRetries = 2;

    const formData = new FormData();
    formData.append('sessionId', this.sessionId);

    // Add batch files to form data
    files.forEach(file => {
      formData.append('videos', file);
    });

    try {
      console.log(`📤 [UPLOAD] Uploading batch ${batchNumber}/${totalBatches}:`, files.map(f => f.name));
      console.log('Session ID:', this.sessionId);

      if (retryCount > 0) {
        console.log(`Retry attempt ${retryCount}/${maxRetries} for batch ${batchNumber}`);
        this.showStatus(`Retrying batch ${batchNumber} (${retryCount}/${maxRetries})...`, 'warning');
      }

      const response = await fetch('/api/upload/files', {
        method: 'POST',
        body: formData
      });

      console.log('Upload response status:', response.status);
      console.log('Upload response headers:', response.headers);

      // Handle 502 Bad Gateway specifically
      if (response.status === 502) {
        if (retryCount < maxRetries) {
          console.log(`502 error, retrying in 3 seconds... (${retryCount + 1}/${maxRetries})`);
          await new Promise(resolve => setTimeout(resolve, 3000));
          return this.uploadFiles(files, retryCount + 1);
        } else {
          throw new Error('Server is temporarily unavailable (502 Bad Gateway). Please try again later.');
        }
      }

      // Check if response is JSON
      const contentType = response.headers.get('content-type');
      if (!contentType || !contentType.includes('application/json')) {
        const text = await response.text();
        console.error('Non-JSON response:', text.substring(0, 500));

        if (response.status >= 500) {
          throw new Error(`Server error (${response.status}). Please try again later.`);
        } else {
          throw new Error(`Server returned non-JSON response (${response.status}). Check server logs.`);
        }
      }

      if (!response.ok) {
        const error = await response.json();

        // Handle specific error types
        if (error.error === 'Too many files') {
          throw new Error(`${error.message}\n\nTip: The app automatically splits files into batches, but you may have selected too many files at once.`);
        }

        throw new Error(error.message || 'Upload failed');
      }

      const result = await response.json();
      console.log(`✅ [UPLOAD] Batch ${batchNumber} result:`, result);

      // Update selected files list
      if (result.files && result.files.length > 0) {
        this.selectedFiles = [...this.selectedFiles, ...result.files];
        this.updateFilesDisplay();

        if (this.onFilesChanged) {
          this.onFilesChanged(this.selectedFiles);
        }
      }

    } catch (error) {
      console.error(`❌ [UPLOAD] Batch ${batchNumber} error:`, error);

      // Retry on network errors for this batch
      if ((error.message.includes('fetch') || error.message.includes('network') || error.message.includes('502')) && retryCount < maxRetries) {
        console.log(`🔄 [UPLOAD] Network error, retrying batch ${batchNumber} in 3 seconds... (${retryCount + 1}/${maxRetries})`);
        await new Promise(resolve => setTimeout(resolve, 3000));
        return this.uploadBatch(files, batchNumber, totalBatches, retryCount + 1);
      }

      throw error;
    }
  }

  updateFilesDisplay() {
    if (this.selectedFiles.length === 0) {
      this.selectedFilesDiv.style.display = 'none';
      return;
    }

    this.selectedFilesDiv.style.display = 'block';
    
    // Update files list
    this.filesList.innerHTML = '';
    this.selectedFiles.forEach(file => {
      const fileItem = this.createFileItem(file);
      this.filesList.appendChild(fileItem);
    });

    // Update summary
    const totalSize = this.selectedFiles.reduce((sum, file) => sum + file.size, 0);
    const totalDuration = this.selectedFiles.reduce((sum, file) => sum + (file.duration || 0), 0);
    
    this.filesSummary.innerHTML = `
      <div>
        <strong>${this.selectedFiles.length}</strong> files selected
      </div>
      <div>
        Total size: <strong>${this.formatFileSize(totalSize)}</strong>
      </div>
      <div>
        Total duration: <strong>${this.formatDuration(totalDuration)}</strong>
      </div>
    `;
  }

  createFileItem(file) {
    const item = document.createElement('div');
    item.className = 'file-item';
    
    item.innerHTML = `
      <div class="file-info">
        <div class="file-name">${file.name}</div>
        <div class="file-details">
          ${this.formatFileSize(file.size)} • ${this.formatDuration(file.duration || 0)}
        </div>
      </div>
      <div class="file-actions">
        <button class="btn btn-small btn-danger" onclick="fileManager.removeFile('${file.id}')">
          Remove
        </button>
      </div>
    `;
    
    return item;
  }

  async removeFile(fileId) {
    try {
      const response = await fetch(`/api/upload/file/${this.sessionId}/${fileId}`, {
        method: 'DELETE'
      });

      if (!response.ok) {
        throw new Error('Failed to remove file');
      }

      // Remove from local list
      this.selectedFiles = this.selectedFiles.filter(file => file.id !== fileId);
      this.updateFilesDisplay();
      
      if (this.onFilesChanged) {
        this.onFilesChanged(this.selectedFiles);
      }

      this.showStatus('File removed successfully', 'success');
    } catch (error) {
      console.error('Remove file error:', error);
      this.showStatus('Failed to remove file', 'error');
    }
  }

  formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  formatDuration(seconds) {
    if (!seconds || seconds === 0) return '0:00';
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  }

  showStatus(message, type = 'info') {
    const statusText = document.getElementById('status-text');
    if (statusText) {
      statusText.textContent = message;
      statusText.className = `status-text ${type}`;
      
      // Clear status after 5 seconds
      setTimeout(() => {
        statusText.textContent = 'Ready';
        statusText.className = 'status-text';
      }, 5000);
    }
  }

  getSessionId() {
    return this.sessionId;
  }

  getSelectedFiles() {
    return this.selectedFiles;
  }

  async cleanup() {
    if (this.sessionId) {
      try {
        await fetch(`/api/session/${this.sessionId}`, {
          method: 'DELETE'
        });
      } catch (error) {
        console.error('Cleanup error:', error);
      }
    }
  }
}

// Global instance
const fileManager = new FileManager();
