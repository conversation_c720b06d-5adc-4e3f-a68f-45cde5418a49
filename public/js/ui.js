// UI State Management
class UIManager {
  constructor() {
    this.currentStep = 1;
    this.totalSteps = 4;
    this.splitterVideoFile = null;
    this.initializeElements();
    this.setupEventListeners();
  }

  initializeElements() {
    // Step elements
    this.stepElements = document.querySelectorAll('.step');
    this.stepContents = document.querySelectorAll('.step-content');
    
    // Navigation buttons
    this.nextToConfigureBtn = document.getElementById('next-to-configure');
    this.backToFilesBtn = document.getElementById('back-to-files');
    this.startGenerationBtn = document.getElementById('start-generation');
    this.cancelGenerationBtn = document.getElementById('cancel-generation');
    this.goToDownloadsBtn = document.getElementById('go-to-downloads');
    this.startNewSessionBtn = document.getElementById('start-new-session');
    this.cleanupFilesBtn = document.getElementById('cleanup-files');
    this.refreshDownloadsBtn = document.getElementById('refresh-downloads');
    this.debugOutputBtn = document.getElementById('debug-output');
    this.downloadAllZipBtn = document.getElementById('download-all-zip');

    // Progress elements
    this.overallProgress = document.getElementById('overall-progress');
    this.overallProgressText = document.getElementById('overall-progress-text');
    this.currentVideoProgress = document.getElementById('current-video-progress');
    this.videoProgress = document.getElementById('video-progress');
    this.videoProgressText = document.getElementById('video-progress-text');
    this.generationLog = document.getElementById('generation-log');
    this.completedVideos = document.getElementById('completed-videos');
    this.completedVideosList = document.getElementById('completed-videos-list');
    this.downloadsList = document.getElementById('downloads-list');

    // Loading overlay
    this.loadingOverlay = document.getElementById('loading-overlay');

    // Status elements
    this.statusText = document.getElementById('status-text');
    this.connectionStatus = document.getElementById('connection-status');

    // Splitter video elements
    this.useSplitterCheckbox = document.getElementById('use-splitter');
    this.splitterSelection = document.getElementById('splitter-selection');
    this.splitterVideoInput = document.getElementById('splitter-video-input');
    this.splitterVideoInfo = document.getElementById('splitter-video-info');
    this.removeSplitterVideoBtn = document.getElementById('remove-splitter-video');
  }

  setupEventListeners() {
    // Navigation
    this.nextToConfigureBtn?.addEventListener('click', () => this.goToStep(2));
    this.backToFilesBtn?.addEventListener('click', () => this.goToStep(1));
    this.startGenerationBtn?.addEventListener('click', () => this.startGeneration());
    this.cancelGenerationBtn?.addEventListener('click', () => this.cancelGeneration());
    this.goToDownloadsBtn?.addEventListener('click', () => this.goToStep(4));
    this.startNewSessionBtn?.addEventListener('click', () => this.startNewSession());
    this.cleanupFilesBtn?.addEventListener('click', () => this.cleanupFiles());
    this.refreshDownloadsBtn?.addEventListener('click', () => this.loadDownloads());
    this.debugOutputBtn?.addEventListener('click', () => this.debugOutput());
    this.downloadAllZipBtn?.addEventListener('click', () => this.downloadAllAsZip());

    // Splitter video handling
    this.setupSplitterVideoHandling();
  }

  setupSplitterVideoHandling() {
    // Show/hide splitter video selection based on checkbox
    if (this.useSplitterCheckbox) {
      this.useSplitterCheckbox.addEventListener('change', () => {
        if (this.useSplitterCheckbox.checked) {
          this.splitterSelection.style.display = 'block';
        } else {
          this.splitterSelection.style.display = 'none';
          this.clearSplitterVideo();
        }
      });
    }

    // Handle splitter video file selection
    if (this.splitterVideoInput) {
      this.splitterVideoInput.addEventListener('change', (e) => {
        const file = e.target.files[0];
        if (file) {
          this.setSplitterVideo(file);
        }
      });
    }

    // Handle splitter video removal
    if (this.removeSplitterVideoBtn) {
      this.removeSplitterVideoBtn.addEventListener('click', () => {
        this.clearSplitterVideo();
      });
    }
  }

  setSplitterVideo(file) {
    this.splitterVideoFile = file;

    if (this.splitterVideoInfo) {
      const nameElement = this.splitterVideoInfo.querySelector('.splitter-video-name');
      if (nameElement) {
        nameElement.textContent = `${file.name} (${this.formatFileSize(file.size)})`;
      }
      this.splitterVideoInfo.style.display = 'flex';
    }
  }

  clearSplitterVideo() {
    this.splitterVideoFile = null;

    if (this.splitterVideoInput) {
      this.splitterVideoInput.value = '';
    }

    if (this.splitterVideoInfo) {
      this.splitterVideoInfo.style.display = 'none';
    }
  }

  goToStep(stepNumber) {
    if (stepNumber < 1 || stepNumber > this.totalSteps) return;

    // Update step indicators
    this.stepElements.forEach((step, index) => {
      const stepNum = index + 1;
      step.classList.remove('active', 'completed');
      
      if (stepNum === stepNumber) {
        step.classList.add('active');
      } else if (stepNum < stepNumber) {
        step.classList.add('completed');
      }
    });

    // Update step content
    this.stepContents.forEach((content, index) => {
      content.classList.remove('active');
      if (index + 1 === stepNumber) {
        content.classList.add('active');
      }
    });

    this.currentStep = stepNumber;

    // Update navigation buttons
    this.updateNavigationButtons();

    // Load step-specific data
    this.loadStepData(stepNumber);
  }

  updateNavigationButtons() {
    // Enable/disable next button based on files
    if (this.nextToConfigureBtn) {
      const hasFiles = fileManager.getSelectedFiles().length > 0;
      this.nextToConfigureBtn.disabled = !hasFiles;
    }
  }

  async loadStepData(stepNumber) {
    switch (stepNumber) {
      case 4: // Downloads step
        console.log('Loading downloads for step 4...');
        await this.loadDownloads();
        break;
    }
  }

  async startGeneration() {
    try {
      this.showLoading('Starting generation...');

      // Get settings
      const settings = this.getGenerationSettings();

      // Validate settings
      if (!this.validateSettings(settings)) {
        this.hideLoading();
        return;
      }

      // Upload splitter video if selected
      let splitterVideoId = null;
      if (settings.useSplitterVideo && settings.splitterVideoFile) {
        this.showLoading('Uploading splitter video...');
        splitterVideoId = await this.uploadSplitterVideo(settings.splitterVideoFile);
      }

      // Set session ID for video processor
      videoProcessor.setSessionId(fileManager.getSessionId());

      // Setup progress callbacks
      this.setupProgressCallbacks();

      // Prepare final settings
      const finalSettings = {
        clipsPerVideo: settings.clipsPerVideo,
        numberOfVideos: settings.numberOfVideos,
        useSplitterVideo: settings.useSplitterVideo,
        splitterVideoId: splitterVideoId
      };

      // Start generation
      await videoProcessor.startGeneration(finalSettings);

      // Go to generation step
      this.goToStep(3);
      this.hideLoading();

    } catch (error) {
      console.error('Generation start error:', error);
      this.showStatus(`Failed to start generation: ${error.message}`, 'error');
      this.hideLoading();
    }
  }

  async uploadSplitterVideo(file) {
    try {
      const sessionId = fileManager.getSessionId();
      const formData = new FormData();
      formData.append('sessionId', sessionId);
      formData.append('video', file);

      const response = await fetch('/api/upload/splitter', {
        method: 'POST',
        body: formData
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Splitter video upload failed');
      }

      const result = await response.json();
      return result.file.id;

    } catch (error) {
      console.error('Splitter video upload error:', error);
      throw new Error(`Failed to upload splitter video: ${error.message}`);
    }
  }

  getGenerationSettings() {
    return {
      clipsPerVideo: parseInt(document.getElementById('clips-per-video')?.value || 10),
      numberOfVideos: parseInt(document.getElementById('number-of-videos')?.value || 1),
      useSplitterVideo: document.getElementById('use-splitter')?.checked || false,
      splitterVideoFile: this.splitterVideoFile
    };
  }

  formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  validateSettings(settings) {
    if (settings.clipsPerVideo < 2 || settings.clipsPerVideo > 50) {
      this.showStatus('Clips per video must be between 2 and 50', 'error');
      return false;
    }

    if (settings.numberOfVideos < 1 || settings.numberOfVideos > 100) {
      this.showStatus('Number of videos must be between 1 and 100', 'error');
      return false;
    }

    if (settings.useSplitterVideo && !settings.splitterVideoFile) {
      this.showStatus('Please select a splitter video file or uncheck the option', 'error');
      return false;
    }

    return true;
  }

  setupProgressCallbacks() {
    videoProcessor.onProgress = (data) => this.handleProgress(data);
    videoProcessor.onVideoCompleted = (data) => this.handleVideoCompleted(data);
    videoProcessor.onGenerationCompleted = (data) => this.handleGenerationCompleted(data);
    videoProcessor.onError = (error) => this.handleGenerationError(error);
  }

  handleProgress(data) {
    switch (data.type) {
      case 'started':
        this.addLogEntry(`Starting generation of ${data.totalVideos} videos with ${data.clipsPerVideo} clips each`);
        break;

      case 'video_started':
        this.addLogEntry(`Starting video ${data.videoIndex} of ${data.totalVideos}`);
        this.updateOverallProgress((data.videoIndex - 1) / data.totalVideos * 100);
        this.showCurrentVideoProgress();
        this.updateVideoProgress(0);
        break;

      case 'video_progress':
        this.updateVideoProgress(data.progress);
        break;
    }
  }

  handleVideoCompleted(data) {
    this.addLogEntry(`Video ${data.videoIndex} completed: ${data.filename}`, 'success');
    this.addCompletedVideo(data);
    this.updateVideoProgress(100);
  }

  handleGenerationCompleted(data) {
    this.addLogEntry(`All ${data.totalVideos} videos completed successfully!`, 'success');
    this.updateOverallProgress(100);
    this.hideCurrentVideoProgress();
    this.showGoToDownloadsButton();

    // Auto-load downloads when generation completes
    setTimeout(() => {
      this.loadDownloads();
    }, 1000);
  }

  handleGenerationError(error) {
    this.addLogEntry(`Generation error: ${error.message}`, 'error');
    this.showStatus(`Generation failed: ${error.message}`, 'error');
  }

  updateOverallProgress(percent) {
    if (this.overallProgress) {
      this.overallProgress.style.width = `${percent}%`;
    }
    if (this.overallProgressText) {
      this.overallProgressText.textContent = `${Math.round(percent)}%`;
    }
  }

  updateVideoProgress(percent) {
    if (this.videoProgress) {
      this.videoProgress.style.width = `${percent}%`;
    }
    if (this.videoProgressText) {
      this.videoProgressText.textContent = `${Math.round(percent)}%`;
    }
  }

  showCurrentVideoProgress() {
    if (this.currentVideoProgress) {
      this.currentVideoProgress.style.display = 'block';
    }
  }

  hideCurrentVideoProgress() {
    if (this.currentVideoProgress) {
      this.currentVideoProgress.style.display = 'none';
    }
  }

  showGoToDownloadsButton() {
    if (this.goToDownloadsBtn) {
      this.goToDownloadsBtn.style.display = 'inline-flex';
    }
  }

  addLogEntry(message, type = 'info') {
    if (!this.generationLog) return;

    const entry = document.createElement('div');
    entry.className = `log-entry ${type}`;
    entry.textContent = `${new Date().toLocaleTimeString()}: ${message}`;
    
    this.generationLog.appendChild(entry);
    this.generationLog.scrollTop = this.generationLog.scrollHeight;
  }

  addCompletedVideo(videoData) {
    if (!this.completedVideosList) return;

    if (this.completedVideos) {
      this.completedVideos.style.display = 'block';
    }

    const videoItem = document.createElement('div');
    videoItem.className = 'completed-video-item';
    videoItem.innerHTML = `
      <div class="video-info">
        <div class="video-name">${videoData.filename}</div>
        <div class="video-actions">
          <button class="btn btn-small btn-primary" onclick="uiManager.downloadVideo('${videoData.filename}')">
            Download
          </button>
        </div>
      </div>
    `;

    this.completedVideosList.appendChild(videoItem);
  }

  async downloadVideo(filename) {
    try {
      await videoProcessor.downloadVideo(filename);
      this.showStatus(`Download started: ${filename}`, 'success');
    } catch (error) {
      this.showStatus(`Download failed: ${error.message}`, 'error');
    }
  }

  cancelGeneration() {
    videoProcessor.cancelGeneration();
    this.addLogEntry('Generation cancelled by user', 'warning');
    this.showStatus('Generation cancelled', 'warning');
  }

  async loadDownloads() {
    try {
      console.log('🔄 [UI] Starting to load downloads...');
      this.showLoading('Loading downloads...');

      const sessionId = fileManager.getSessionId();
      console.log(`🔑 [UI] Session ID for downloads: ${sessionId}`);

      if (!sessionId) {
        console.error('❌ [UI] No session ID available for downloads');
        this.displayDownloads([]);
        this.hideLoading();
        return;
      }

      // Set session ID for video processor if not already set
      videoProcessor.setSessionId(sessionId);
      console.log(`🔗 [UI] Video processor session ID set to: ${sessionId}`);

      // First, let's check the debug endpoint to see what's actually in the output directory
      try {
        console.log(`🔍 [UI] Checking debug output for session: ${sessionId}`);
        const debugResponse = await fetch(`/debug/output/${sessionId}`);
        const debugData = await debugResponse.json();
        console.log(`🔍 [UI] Debug output data:`, debugData);
      } catch (debugError) {
        console.warn(`⚠️ [UI] Debug endpoint failed:`, debugError);
      }

      const result = await videoProcessor.getDownloadsList();
      console.log(`📋 [UI] Downloads result:`, result);

      this.displayDownloads(result.files || []);

      this.hideLoading();
    } catch (error) {
      console.error('❌ [UI] Load downloads error:', error);
      this.showStatus(`Failed to load downloads: ${error.message}`, 'error');
      this.displayDownloads([]); // Show empty state
      this.hideLoading();
    }
  }

  displayDownloads(files) {
    console.log('Displaying downloads:', files);

    if (!this.downloadsList) {
      console.error('Downloads list element not found');
      return;
    }

    // Show/hide the "Download All as ZIP" button based on available files
    if (this.downloadAllZipBtn) {
      if (files && files.length > 0) {
        this.downloadAllZipBtn.style.display = 'inline-flex';
      } else {
        this.downloadAllZipBtn.style.display = 'none';
      }
    }

    if (!files || files.length === 0) {
      this.downloadsList.innerHTML = `
        <div class="no-downloads" style="
          text-align: center;
          padding: 40px;
          color: var(--text-muted);
          background: var(--bg-secondary);
          border-radius: var(--radius-lg);
        ">
          <div style="font-size: 1.2rem; margin-bottom: 10px;">📁 No downloads available</div>
          <div>Generate some videos first to see downloads here</div>
        </div>
      `;
      return;
    }

    this.downloadsList.innerHTML = '';

    files.forEach(file => {
      const fileItem = document.createElement('div');
      fileItem.className = 'download-item';
      fileItem.style.cssText = `
        background: var(--bg-secondary);
        border-radius: var(--radius-md);
        padding: var(--spacing-lg);
        margin-bottom: var(--spacing-md);
        display: flex;
        justify-content: space-between;
        align-items: center;
      `;

      fileItem.innerHTML = `
        <div class="download-info">
          <div class="download-name" style="font-weight: 500; margin-bottom: 5px;">${file.filename}</div>
          <div class="download-details" style="color: var(--text-muted); font-size: 0.9rem;">
            ${this.formatFileSize(file.size)} • Created: ${new Date(file.created).toLocaleString()}
          </div>
        </div>
        <div class="download-actions" style="display: flex; gap: 10px;">
          <button class="btn btn-small btn-primary" onclick="uiManager.downloadVideo('${file.filename}')">
            📥 Download
          </button>
          <button class="btn btn-small btn-danger" onclick="uiManager.deleteDownload('${file.filename}')">
            🗑️ Delete
          </button>
        </div>
      `;

      this.downloadsList.appendChild(fileItem);
    });

    console.log(`Displayed ${files.length} download items`);
  }

  async deleteDownload(filename) {
    if (!confirm(`Are you sure you want to delete ${filename}?`)) {
      return;
    }

    try {
      await videoProcessor.deleteVideo(filename);
      this.showStatus(`File deleted: ${filename}`, 'success');
      await this.loadDownloads(); // Refresh list
    } catch (error) {
      this.showStatus(`Delete failed: ${error.message}`, 'error');
    }
  }

  async downloadAllAsZip() {
    try {
      const sessionId = fileManager.getSessionId();
      if (!sessionId) {
        this.showStatus('No active session found', 'error');
        return;
      }

      // Check if there are any downloads available
      const result = await videoProcessor.getDownloadsList();
      const files = result.files || [];

      if (files.length === 0) {
        this.showStatus('No videos available for download', 'warning');
        return;
      }

      // Show loading state
      this.showStatus(`Creating ZIP archive with ${files.length} videos...`, 'info');

      // Disable the button and show loading state
      if (this.downloadAllZipBtn) {
        this.downloadAllZipBtn.disabled = true;
        this.downloadAllZipBtn.textContent = '📦 Creating ZIP...';
      }

      // Create download URL
      const zipUrl = `/api/download/zip/${sessionId}`;

      // Create a temporary link and trigger download
      const link = document.createElement('a');
      link.href = zipUrl;
      link.style.display = 'none';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      this.showStatus(`ZIP download started with ${files.length} videos`, 'success');

    } catch (error) {
      console.error('Download all ZIP error:', error);
      this.showStatus(`Failed to create ZIP archive: ${error.message}`, 'error');
    } finally {
      // Re-enable the button
      if (this.downloadAllZipBtn) {
        this.downloadAllZipBtn.disabled = false;
        this.downloadAllZipBtn.textContent = '📦 Download All as ZIP';
      }
    }
  }

  async startNewSession() {
    if (confirm('This will clear all current files and start a new session. Continue?')) {
      // Cleanup current session
      await fileManager.cleanup();
      videoProcessor.cleanup();
      
      // Reset UI
      this.goToStep(1);
      this.resetProgress();
      this.clearLogs();
      
      // Reload page to reset everything
      window.location.reload();
    }
  }

  async cleanupFiles() {
    if (confirm('This will delete all generated files. Continue?')) {
      try {
        this.showLoading('Cleaning up files...');

        // Implementation would depend on server endpoint
        this.showStatus('Files cleaned up successfully', 'success');
        await this.loadDownloads(); // Refresh list

        this.hideLoading();
      } catch (error) {
        this.showStatus(`Cleanup failed: ${error.message}`, 'error');
        this.hideLoading();
      }
    }
  }

  async debugOutput() {
    try {
      const sessionId = fileManager.getSessionId();
      console.log(`🔍 [DEBUG] Debugging output for session: ${sessionId}`);

      // Check all sessions first
      const allSessionsResponse = await fetch('/debug/output');
      const allSessionsData = await allSessionsResponse.json();
      console.log(`🔍 [DEBUG] All sessions data:`, allSessionsData);

      if (sessionId) {
        // Check specific session
        const sessionResponse = await fetch(`/debug/output/${sessionId}`);
        const sessionData = await sessionResponse.json();
        console.log(`🔍 [DEBUG] Session ${sessionId} data:`, sessionData);

        // Show alert with debug info
        alert(`Debug Info for Session ${sessionId}:\n\n` +
              `Directory exists: ${sessionData.exists}\n` +
              `Files found: ${sessionData.files?.length || 0}\n` +
              `Files: ${sessionData.files?.map(f => f.filename).join(', ') || 'None'}\n\n` +
              `Check console for detailed logs.`);
      } else {
        alert('No session ID available for debugging');
      }

    } catch (error) {
      console.error('❌ [DEBUG] Debug output error:', error);
      alert(`Debug failed: ${error.message}`);
    }
  }

  resetProgress() {
    this.updateOverallProgress(0);
    this.updateVideoProgress(0);
    this.hideCurrentVideoProgress();
    
    if (this.completedVideos) {
      this.completedVideos.style.display = 'none';
    }
    
    if (this.completedVideosList) {
      this.completedVideosList.innerHTML = '';
    }
    
    if (this.goToDownloadsBtn) {
      this.goToDownloadsBtn.style.display = 'none';
    }
  }

  clearLogs() {
    if (this.generationLog) {
      this.generationLog.innerHTML = '<div class="log-entry">Ready for generation...</div>';
    }
  }

  showLoading(message = 'Loading...') {
    if (this.loadingOverlay) {
      this.loadingOverlay.style.display = 'flex';
      const loadingText = this.loadingOverlay.querySelector('.loading-text');
      if (loadingText) {
        loadingText.textContent = message;
      }
    }
  }

  hideLoading() {
    if (this.loadingOverlay) {
      this.loadingOverlay.style.display = 'none';
    }
  }

  showStatus(message, type = 'info') {
    if (this.statusText) {
      this.statusText.textContent = message;
      this.statusText.className = `status-text ${type}`;
      
      // Clear status after 5 seconds
      setTimeout(() => {
        this.statusText.textContent = 'Ready';
        this.statusText.className = 'status-text';
      }, 5000);
    }
  }

  updateConnectionStatus(connected) {
    if (this.connectionStatus) {
      const indicator = this.connectionStatus.querySelector('.status-indicator');
      const text = this.connectionStatus.querySelector('span:last-child');
      
      if (indicator) {
        indicator.className = connected ? 'status-indicator' : 'status-indicator disconnected';
      }
      
      if (text) {
        text.textContent = connected ? 'Connected' : 'Disconnected';
      }
    }
  }
}

// Global instance
const uiManager = new UIManager();
