// Video Processor with Server-Sent Events
class VideoProcessor {
  constructor() {
    this.sessionId = null;
    this.eventSource = null;
    this.isGenerating = false;
    this.completedVideos = [];
    this.onProgress = null;
    this.onVideoCompleted = null;
    this.onGenerationCompleted = null;
    this.onError = null;
  }

  setSessionId(sessionId) {
    this.sessionId = sessionId;
  }

  async startGeneration(settings) {
    if (!this.sessionId) {
      throw new Error('No session ID set');
    }

    if (this.isGenerating) {
      throw new Error('Generation already in progress');
    }

    try {
      this.isGenerating = true;
      this.completedVideos = [];

      // Start progress monitoring
      this.startProgressMonitoring();

      // Start generation
      const response = await fetch(`/api/generate/${this.sessionId}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(settings)
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Generation failed to start');
      }

      const result = await response.json();
      console.log('Generation started:', result);

    } catch (error) {
      this.isGenerating = false;
      this.stopProgressMonitoring();
      throw error;
    }
  }

  startProgressMonitoring() {
    if (this.eventSource) {
      this.eventSource.close();
    }

    this.eventSource = new EventSource(`/api/progress/${this.sessionId}`);

    this.eventSource.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data);
        this.handleProgressUpdate(data);
      } catch (error) {
        console.error('Progress parsing error:', error);
      }
    };

    this.eventSource.onerror = (error) => {
      console.error('EventSource error:', error);
      
      // Try to reconnect after a delay
      setTimeout(() => {
        if (this.isGenerating && this.sessionId) {
          this.startProgressMonitoring();
        }
      }, 5000);
    };
  }

  handleProgressUpdate(data) {
    console.log('Progress update:', data);

    switch (data.type) {
      case 'generation_started':
        if (this.onProgress) {
          this.onProgress({
            type: 'started',
            totalVideos: data.totalVideos,
            clipsPerVideo: data.clipsPerVideo
          });
        }
        break;

      case 'video_started':
        if (this.onProgress) {
          this.onProgress({
            type: 'video_started',
            videoIndex: data.videoIndex,
            totalVideos: data.totalVideos
          });
        }
        break;

      case 'video_progress':
        if (this.onProgress) {
          this.onProgress({
            type: 'video_progress',
            videoIndex: data.videoIndex,
            progress: data.progress,
            filename: data.filename
          });
        }
        break;

      case 'video_completed':
        this.completedVideos.push({
          filename: data.filename,
          downloadUrl: data.downloadUrl,
          videoIndex: data.videoIndex
        });

        if (this.onVideoCompleted) {
          this.onVideoCompleted({
            filename: data.filename,
            downloadUrl: data.downloadUrl,
            videoIndex: data.videoIndex
          });
        }
        break;

      case 'generation_completed':
        this.isGenerating = false;
        this.stopProgressMonitoring();

        if (this.onGenerationCompleted) {
          this.onGenerationCompleted({
            totalVideos: data.totalVideos,
            completedVideos: this.completedVideos
          });
        }
        break;

      case 'error':
        this.isGenerating = false;
        this.stopProgressMonitoring();

        if (this.onError) {
          this.onError(new Error(data.message || 'Generation error'));
        }
        break;

      default:
        console.log('Unknown progress type:', data.type);
    }
  }

  stopProgressMonitoring() {
    if (this.eventSource) {
      this.eventSource.close();
      this.eventSource = null;
    }
  }

  cancelGeneration() {
    this.isGenerating = false;
    this.stopProgressMonitoring();
    
    // Note: Server-side cancellation would require additional implementation
    console.log('Generation cancelled');
  }

  async getGenerationStatus() {
    if (!this.sessionId) {
      throw new Error('No session ID set');
    }

    try {
      const response = await fetch(`/api/generate/status/${this.sessionId}`);
      
      if (!response.ok) {
        throw new Error('Failed to get status');
      }

      return await response.json();
    } catch (error) {
      console.error('Status check error:', error);
      throw error;
    }
  }

  async getDownloadsList() {
    if (!this.sessionId) {
      throw new Error('No session ID set');
    }

    try {
      console.log(`Fetching downloads for session: ${this.sessionId}`);
      const url = `/api/download/list/${this.sessionId}`;
      console.log(`Downloads URL: ${url}`);

      const response = await fetch(url);
      console.log(`Downloads response status: ${response.status}`);

      if (!response.ok) {
        const errorText = await response.text();
        console.error('Downloads error response:', errorText);
        throw new Error(`Failed to get downloads list (${response.status}): ${errorText}`);
      }

      const result = await response.json();
      console.log('Downloads response data:', result);
      return result;
    } catch (error) {
      console.error('Downloads list error:', error);
      throw error;
    }
  }

  async downloadVideo(filename) {
    if (!this.sessionId) {
      throw new Error('No session ID set');
    }

    try {
      const url = `/api/download/${this.sessionId}/${filename}`;
      
      // Create a temporary link and trigger download
      const link = document.createElement('a');
      link.href = url;
      link.download = filename;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      console.log('Download started:', filename);
    } catch (error) {
      console.error('Download error:', error);
      throw error;
    }
  }

  async deleteVideo(filename) {
    if (!this.sessionId) {
      throw new Error('No session ID set');
    }

    try {
      const response = await fetch(`/api/download/${this.sessionId}/${filename}`, {
        method: 'DELETE'
      });

      if (!response.ok) {
        throw new Error('Failed to delete video');
      }

      return await response.json();
    } catch (error) {
      console.error('Delete video error:', error);
      throw error;
    }
  }

  getCompletedVideos() {
    return this.completedVideos;
  }

  isProcessing() {
    return this.isGenerating;
  }

  cleanup() {
    this.stopProgressMonitoring();
    this.isGenerating = false;
    this.completedVideos = [];
  }
}

// Global instance
const videoProcessor = new VideoProcessor();
