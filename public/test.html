<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LazyRemixer - Upload Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #1a1a1a;
            color: #fff;
        }
        .test-section {
            background: #2a2a2a;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
        }
        button {
            background: #4a9eff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #3a8ae6;
        }
        .result {
            background: #333;
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .error {
            background: #ff4757;
            color: white;
        }
        .success {
            background: #00d084;
            color: white;
        }
        input[type="file"] {
            margin: 10px 0;
            padding: 10px;
            background: #333;
            color: white;
            border: 1px solid #555;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <h1>🎬 LazyRemixer Upload Test</h1>
    
    <div class="test-section">
        <h2>1. Test Server Connection</h2>
        <button onclick="testHealth()">Test Health Endpoint</button>
        <button onclick="testFFmpeg()">Test FFmpeg</button>
        <div id="connection-result" class="result"></div>
    </div>

    <div class="test-section">
        <h2>2. Test Session Creation</h2>
        <button onclick="createSession()">Create Session</button>
        <div id="session-result" class="result"></div>
    </div>

    <div class="test-section">
        <h2>3. Test File Upload</h2>
        <input type="file" id="file-input" multiple accept="video/*">
        <br>
        <button onclick="uploadFiles()">Upload Files</button>
        <div id="upload-result" class="result"></div>
    </div>

    <div class="test-section">
        <h2>4. Debug Information</h2>
        <button onclick="showDebugInfo()">Show Debug Info</button>
        <div id="debug-result" class="result"></div>
    </div>

    <script>
        let sessionId = null;

        async function testHealth() {
            const result = document.getElementById('connection-result');
            try {
                const response = await fetch('/health');
                const data = await response.json();
                result.textContent = `✅ Health Check: ${JSON.stringify(data, null, 2)}`;
                result.className = 'result success';
            } catch (error) {
                result.textContent = `❌ Health Check Failed: ${error.message}`;
                result.className = 'result error';
            }
        }

        async function testFFmpeg() {
            const result = document.getElementById('connection-result');
            try {
                const response = await fetch('/debug/ffmpeg');
                const data = await response.json();
                result.textContent = `🎬 FFmpeg Test: ${JSON.stringify(data, null, 2)}`;
                result.className = 'result success';
            } catch (error) {
                result.textContent = `❌ FFmpeg Test Failed: ${error.message}`;
                result.className = 'result error';
            }
        }

        async function createSession() {
            const result = document.getElementById('session-result');
            try {
                const response = await fetch('/api/session', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}`);
                }
                
                const data = await response.json();
                sessionId = data.sessionId;
                result.textContent = `✅ Session Created: ${JSON.stringify(data, null, 2)}`;
                result.className = 'result success';
            } catch (error) {
                result.textContent = `❌ Session Creation Failed: ${error.message}`;
                result.className = 'result error';
            }
        }

        async function uploadFiles() {
            const result = document.getElementById('upload-result');
            const fileInput = document.getElementById('file-input');
            
            if (!sessionId) {
                result.textContent = '❌ Please create a session first';
                result.className = 'result error';
                return;
            }

            if (!fileInput.files.length) {
                result.textContent = '❌ Please select files first';
                result.className = 'result error';
                return;
            }

            try {
                const formData = new FormData();
                formData.append('sessionId', sessionId);
                
                for (const file of fileInput.files) {
                    formData.append('videos', file);
                }

                result.textContent = '⏳ Uploading files...';
                result.className = 'result';

                const response = await fetch('/api/upload/files', {
                    method: 'POST',
                    body: formData
                });

                console.log('Response status:', response.status);
                console.log('Response headers:', [...response.headers.entries()]);

                const contentType = response.headers.get('content-type');
                if (!contentType || !contentType.includes('application/json')) {
                    const text = await response.text();
                    throw new Error(`Server returned non-JSON response: ${text.substring(0, 200)}...`);
                }

                const data = await response.json();
                
                if (!response.ok) {
                    throw new Error(data.message || `HTTP ${response.status}`);
                }

                result.textContent = `✅ Upload Success: ${JSON.stringify(data, null, 2)}`;
                result.className = 'result success';
            } catch (error) {
                result.textContent = `❌ Upload Failed: ${error.message}`;
                result.className = 'result error';
                console.error('Upload error:', error);
            }
        }

        function showDebugInfo() {
            const result = document.getElementById('debug-result');
            const info = {
                userAgent: navigator.userAgent,
                sessionId: sessionId,
                fileSystemAccessSupported: 'showDirectoryPicker' in window,
                location: window.location.href,
                timestamp: new Date().toISOString()
            };
            result.textContent = JSON.stringify(info, null, 2);
            result.className = 'result';
        }

        // Auto-test on load
        window.addEventListener('load', () => {
            testHealth();
        });
    </script>
</body>
</html>
