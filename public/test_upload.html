<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Upload Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #1a1a1a;
            color: #ffffff;
        }
        .test-section {
            background: #2a2a2a;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
        }
        button {
            background: #4a9eff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #3a8eff;
        }
        #log {
            background: #0a0a0a;
            padding: 10px;
            border-radius: 4px;
            height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
        }
        input[type="file"] {
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>LazyRemixer Upload Test</h1>
    
    <div class="test-section">
        <h2>Sequential Upload Test</h2>
        <input type="file" id="fileInput" multiple accept="video/*">
        <br>
        <button onclick="testSequentialUpload()">Test Sequential Upload</button>
        <button onclick="clearLog()">Clear Log</button>
    </div>
    
    <div class="test-section">
        <h2>Upload Log</h2>
        <div id="log"></div>
    </div>

    <script>
        let sessionId = null;
        
        function log(message) {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.textContent += `[${timestamp}] ${message}\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }
        
        function clearLog() {
            document.getElementById('log').textContent = '';
        }
        
        async function createSession() {
            try {
                const response = await fetch('/api/session', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                
                if (!response.ok) {
                    throw new Error('Failed to create session');
                }
                
                const data = await response.json();
                sessionId = data.sessionId;
                log(`✅ Session created: ${sessionId}`);
                return sessionId;
            } catch (error) {
                log(`❌ Session creation failed: ${error.message}`);
                throw error;
            }
        }
        
        async function uploadSingleFile(file, fileNumber, totalFiles) {
            const formData = new FormData();
            formData.append('sessionId', sessionId);
            formData.append('video', file);
            
            try {
                log(`📤 Uploading file ${fileNumber}/${totalFiles}: ${file.name}`);
                
                const response = await fetch('/api/upload/file', {
                    method: 'POST',
                    body: formData
                });
                
                if (!response.ok) {
                    const error = await response.json();
                    throw new Error(error.message || 'Upload failed');
                }
                
                const result = await response.json();
                log(`✅ File ${fileNumber} uploaded successfully: ${file.name} (${result.file.duration}s)`);
                return result.file;
                
            } catch (error) {
                log(`❌ File ${fileNumber} upload failed: ${file.name} - ${error.message}`);
                throw error;
            }
        }
        
        async function testSequentialUpload() {
            const fileInput = document.getElementById('fileInput');
            const files = Array.from(fileInput.files);
            
            if (files.length === 0) {
                log('❌ No files selected');
                return;
            }
            
            log(`📦 Starting sequential upload test with ${files.length} files`);
            
            try {
                // Create session
                await createSession();
                
                const uploadedFiles = [];
                const failedFiles = [];
                
                // Upload files sequentially
                for (let i = 0; i < files.length; i++) {
                    const file = files[i];
                    const fileNumber = i + 1;
                    
                    try {
                        const uploadedFile = await uploadSingleFile(file, fileNumber, files.length);
                        uploadedFiles.push(uploadedFile);
                        
                        // Small delay between uploads
                        if (i < files.length - 1) {
                            await new Promise(resolve => setTimeout(resolve, 500));
                        }
                        
                    } catch (error) {
                        failedFiles.push({ name: file.name, error: error.message });
                    }
                }
                
                // Summary
                log(`📊 Upload completed:`);
                log(`   ✅ Successful: ${uploadedFiles.length}`);
                log(`   ❌ Failed: ${failedFiles.length}`);
                
                if (uploadedFiles.length > 0) {
                    log(`   Successful files: ${uploadedFiles.map(f => f.name).join(', ')}`);
                }
                
                if (failedFiles.length > 0) {
                    log(`   Failed files: ${failedFiles.map(f => f.name).join(', ')}`);
                }
                
            } catch (error) {
                log(`❌ Upload test failed: ${error.message}`);
            }
        }
        
        // Initialize
        log('🚀 Upload test page loaded');
        log('📝 Instructions:');
        log('   1. Select video files using the file input');
        log('   2. Click "Test Sequential Upload" to test the new upload system');
        log('   3. Watch the log for detailed progress information');
    </script>
</body>
</html>
