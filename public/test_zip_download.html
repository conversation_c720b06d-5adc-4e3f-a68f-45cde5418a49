<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ZIP Download Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #1a1a1a;
            color: #ffffff;
        }
        .test-section {
            background: #2a2a2a;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
        }
        button {
            background: #4a9eff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #3a8eff;
        }
        button:disabled {
            background: #666;
            cursor: not-allowed;
        }
        .btn-success {
            background: #00d084;
        }
        .btn-success:hover {
            background: #00b574;
        }
        #log {
            background: #0a0a0a;
            padding: 10px;
            border-radius: 4px;
            height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
        }
        input {
            margin: 10px 0;
            padding: 8px;
            border-radius: 4px;
            border: 1px solid #555;
            background: #333;
            color: white;
            width: 300px;
        }
    </style>
</head>
<body>
    <h1>ZIP Download Test</h1>
    
    <div class="test-section">
        <h2>Test ZIP Download</h2>
        <p>Session ID: <strong>bda71ab7-d01d-46b3-89d1-ce6a5dcc890e</strong> (has 4 test videos including large file)</p>
        <button id="testZipBtn" class="btn-success" onclick="testZipDownload()">📦 Test ZIP Download</button>
        <button onclick="listDownloads()">📋 List Downloads</button>
        <button onclick="clearLog()">Clear Log</button>
    </div>
    
    <div class="test-section">
        <h2>Custom Session Test</h2>
        <input type="text" id="sessionInput" placeholder="Enter session ID" value="bda71ab7-d01d-46b3-89d1-ce6a5dcc890e">
        <br>
        <button onclick="testCustomSession()">Test Custom Session</button>
    </div>
    
    <div class="test-section">
        <h2>Test Log</h2>
        <div id="log"></div>
    </div>

    <script>
        let eventSource = null;
        
        function log(message) {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.textContent += `[${timestamp}] ${message}\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }
        
        function clearLog() {
            document.getElementById('log').textContent = '';
        }
        
        function startProgressMonitoring(sessionId) {
            if (eventSource) {
                eventSource.close();
            }
            
            eventSource = new EventSource(`/api/progress/${sessionId}`);
            
            eventSource.onmessage = (event) => {
                try {
                    const data = JSON.parse(event.data);
                    handleProgressUpdate(data);
                } catch (error) {
                    log(`❌ Progress parsing error: ${error.message}`);
                }
            };
            
            eventSource.onerror = (error) => {
                log(`❌ EventSource error: ${error}`);
            };
        }
        
        function handleProgressUpdate(data) {
            switch (data.type) {
                case 'zip_started':
                    log(`📦 ZIP creation started: ${data.message}`);
                    break;
                case 'zip_progress':
                    log(`📦 ZIP progress: ${data.processed}/${data.total} files (${data.percent}%)`);
                    break;
                case 'zip_completed':
                    log(`✅ ZIP creation completed: ${data.message}`);
                    break;
                default:
                    log(`📊 Progress update: ${data.type} - ${JSON.stringify(data)}`);
            }
        }
        
        async function listDownloads() {
            const sessionId = document.getElementById('sessionInput').value;
            
            try {
                log(`📋 Listing downloads for session: ${sessionId}`);
                
                const response = await fetch(`/api/download/list/${sessionId}`);
                
                if (!response.ok) {
                    const error = await response.json();
                    throw new Error(error.error || 'Failed to list downloads');
                }
                
                const result = await response.json();
                log(`📋 Found ${result.files.length} files:`);
                
                result.files.forEach((file, index) => {
                    log(`   ${index + 1}. ${file.filename} (${file.size} bytes)`);
                });
                
            } catch (error) {
                log(`❌ List downloads error: ${error.message}`);
            }
        }
        
        async function testZipDownload() {
            const sessionId = document.getElementById('sessionInput').value;
            const testBtn = document.getElementById('testZipBtn');
            
            try {
                log(`📦 Testing ZIP download for session: ${sessionId}`);
                
                // Start progress monitoring
                startProgressMonitoring(sessionId);
                
                // Disable button and show loading state
                testBtn.disabled = true;
                testBtn.textContent = '📦 Creating ZIP...';
                
                // Create download URL
                const zipUrl = `/api/download/zip/${sessionId}`;
                log(`📦 ZIP URL: ${zipUrl}`);
                
                // Create a temporary link and trigger download
                const link = document.createElement('a');
                link.href = zipUrl;
                link.style.display = 'none';
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
                
                log(`✅ ZIP download initiated successfully`);
                
            } catch (error) {
                log(`❌ ZIP download error: ${error.message}`);
            } finally {
                // Re-enable button after a delay
                setTimeout(() => {
                    testBtn.disabled = false;
                    testBtn.textContent = '📦 Test ZIP Download';
                }, 3000);
            }
        }
        
        async function testCustomSession() {
            const sessionId = document.getElementById('sessionInput').value;
            
            if (!sessionId) {
                log('❌ Please enter a session ID');
                return;
            }
            
            log(`🔍 Testing session: ${sessionId}`);
            await listDownloads();
        }
        
        // Initialize
        log('🚀 ZIP Download test page loaded');
        log('📝 Instructions:');
        log('   1. Click "List Downloads" to see available files');
        log('   2. Click "Test ZIP Download" to download all files as ZIP');
        log('   3. Monitor the log for progress updates');
        log('   4. Check your browser downloads folder for the ZIP file');
    </script>
</body>
</html>
