const express = require('express');
const path = require('path');
const fs = require('fs-extra');
const archiver = require('archiver');

const router = express.Router();

// Test endpoint to verify route is working
router.get('/test', (req, res) => {
  console.log('🧪 [DOWNLOADS] Test endpoint called');
  console.log('🧪 [DOWNLOADS] app.locals:', Object.keys(req.app.locals));
  console.log('🧪 [DOWNLOADS] activeSessions type:', typeof req.app.locals.activeSessions);
  console.log('🧪 [DOWNLOADS] activeSessions size:', req.app.locals.activeSessions?.size);

  res.json({
    message: 'Download route is working',
    appLocalsKeys: Object.keys(req.app.locals),
    activeSessionsType: typeof req.app.locals.activeSessions,
    activeSessionsSize: req.app.locals.activeSessions?.size || 0,
    activeSessionsKeys: req.app.locals.activeSessions ? Array.from(req.app.locals.activeSessions.keys()) : []
  });
});

// List available downloads for a session (MUST come before /:sessionId/:filename)
router.get('/list/:sessionId', async (req, res) => {
  console.log(`🚀 [DOWNLOADS] Route /list/:sessionId called with sessionId: ${req.params.sessionId}`);
  try {
    const { sessionId } = req.params;
    console.log(`🔍 [DOWNLOADS] Listing downloads for session: ${sessionId}`);

    // Debug app.locals
    console.log(`🔍 [DOWNLOADS] req.app.locals.activeSessions type:`, typeof req.app.locals.activeSessions);
    console.log(`🔍 [DOWNLOADS] req.app.locals.activeSessions:`, req.app.locals.activeSessions);
    console.log(`🔍 [DOWNLOADS] req.app.locals.activeSessions.size:`, req.app.locals.activeSessions?.size);

    // Validate session
    const session = req.app.locals.activeSessions.get(sessionId);
    if (!session) {
      console.error(`❌ [DOWNLOADS] Session not found: ${sessionId}`);
      console.log(`📊 [DOWNLOADS] Active sessions: ${Array.from(req.app.locals.activeSessions.keys())}`);
      return res.status(404).json({ error: 'Session not found' });
    }

    console.log(`✅ [DOWNLOADS] Session found: ${sessionId}`);
    console.log(`📁 [DOWNLOADS] Session data:`, {
      id: session.id,
      created: session.created,
      filesCount: session.files?.length || 0,
      status: session.status
    });

    const sessionOutputDir = path.join(req.app.locals.outputDir, sessionId);
    console.log(`📂 [DOWNLOADS] Output directory path: ${sessionOutputDir}`);

    // Check if output directory exists
    const dirExists = await fs.pathExists(sessionOutputDir);
    console.log(`📂 [DOWNLOADS] Directory exists: ${dirExists}`);

    if (!dirExists) {
      console.log(`📂 [DOWNLOADS] Directory doesn't exist, returning empty list`);
      return res.json({ files: [] });
    }

    // Read directory contents
    const files = await fs.readdir(sessionOutputDir);
    console.log(`📂 [DOWNLOADS] Files in directory: ${files.length}`, files);

    const videoFiles = files.filter(file =>
      file.endsWith('.mp4') ||
      file.endsWith('.avi') ||
      file.endsWith('.mov') ||
      file.endsWith('.mkv') ||
      file.endsWith('.webm')
    );
    console.log(`🎬 [DOWNLOADS] Video files found: ${videoFiles.length}`, videoFiles);

    // Get file information
    const fileInfos = await Promise.all(
      videoFiles.map(async (filename) => {
        const filePath = path.join(sessionOutputDir, filename);
        console.log(`📄 [DOWNLOADS] Processing file: ${filename} at ${filePath}`);

        try {
          const stats = await fs.stat(filePath);
          console.log(`📊 [DOWNLOADS] File stats for ${filename}:`, {
            size: stats.size,
            created: stats.birthtime,
            modified: stats.mtime
          });

          return {
            filename,
            size: stats.size,
            created: stats.birthtime,
            modified: stats.mtime,
            downloadUrl: `/api/download/${sessionId}/${filename}`,
            streamUrl: `/api/download/stream/${sessionId}/${filename}`
          };
        } catch (statError) {
          console.error(`❌ [DOWNLOADS] Error getting stats for ${filename}:`, statError);
          return null;
        }
      })
    );

    // Filter out null entries (files that couldn't be processed)
    const validFileInfos = fileInfos.filter(info => info !== null);
    console.log(`✅ [DOWNLOADS] Valid files processed: ${validFileInfos.length}`);
    console.log(`📋 [DOWNLOADS] Returning file list:`, validFileInfos.map(f => ({ name: f.filename, size: f.size })));

    res.json({ files: validFileInfos });

  } catch (error) {
    console.error('❌ [DOWNLOADS] List downloads error:', error);
    res.status(500).json({
      error: 'Failed to list downloads',
      message: error.message
    });
  }
});

// Download all videos as ZIP
router.get('/zip/:sessionId', async (req, res) => {
  try {
    const { sessionId } = req.params;
    console.log(`📦 [ZIP] Creating ZIP archive for session: ${sessionId}`);

    // Validate session
    const session = req.app.locals.activeSessions.get(sessionId);
    if (!session) {
      return res.status(404).json({ error: 'Session not found' });
    }

    const sessionOutputDir = path.join(req.app.locals.outputDir, sessionId);

    // Check if output directory exists
    if (!await fs.pathExists(sessionOutputDir)) {
      return res.status(404).json({ error: 'No videos found for this session' });
    }

    // Get all video files
    const files = await fs.readdir(sessionOutputDir);
    const videoFiles = files.filter(file =>
      file.endsWith('.mp4') ||
      file.endsWith('.avi') ||
      file.endsWith('.mov') ||
      file.endsWith('.mkv') ||
      file.endsWith('.webm')
    );

    if (videoFiles.length === 0) {
      return res.status(404).json({ error: 'No videos found for this session' });
    }

    console.log(`📦 [ZIP] Found ${videoFiles.length} videos to archive`);

    // Send progress update that ZIP creation is starting
    req.app.locals.sendProgress(sessionId, {
      type: 'zip_started',
      totalFiles: videoFiles.length,
      message: `Creating ZIP archive with ${videoFiles.length} videos...`
    });

    // Set response headers for ZIP download
    const zipFilename = `LazyRemixer_${sessionId}_${new Date().toISOString().slice(0, 10)}.zip`;
    res.setHeader('Content-Type', 'application/zip');
    res.setHeader('Content-Disposition', `attachment; filename="${zipFilename}"`);
    res.setHeader('Cache-Control', 'no-cache');
    res.setHeader('Transfer-Encoding', 'chunked');

    // Handle client disconnect
    req.on('close', () => {
      console.log('📦 [ZIP] Client disconnected, aborting ZIP creation');
      if (archive) {
        archive.abort();
      }
    });

    // Set a timeout to prevent hanging (5 minutes for large files)
    const timeout = setTimeout(() => {
      console.error('📦 [ZIP] ZIP creation timeout after 5 minutes');
      if (archive) {
        archive.abort();
      }
      if (!res.headersSent) {
        res.status(500).json({ error: 'ZIP creation timeout' });
      }
    }, 5 * 60 * 1000);

    // Create archiver instance with better options
    const archive = archiver('zip', {
      zlib: { level: 1 }, // Fastest compression for large video files
      forceLocalTime: true,
      store: false // Don't store uncompressed
    });

    // Handle archiver errors
    archive.on('error', (err) => {
      console.error('📦 [ZIP] Archive error:', err);
      if (!res.headersSent) {
        res.status(500).json({ error: 'Failed to create ZIP archive' });
      } else {
        res.destroy();
      }
    });

    // Handle archiver warnings
    archive.on('warning', (err) => {
      if (err.code === 'ENOENT') {
        console.warn('📦 [ZIP] Archive warning:', err);
      } else {
        console.error('📦 [ZIP] Archive error:', err);
        if (!res.headersSent) {
          res.status(500).json({ error: 'Archive warning became error' });
        }
      }
    });

    // Send progress updates during ZIP creation
    archive.on('progress', (progress) => {
      console.log(`📦 [ZIP] Progress: ${progress.entries.processed}/${progress.entries.total} files`);

      // Send progress update via SSE if available
      req.app.locals.sendProgress(sessionId, {
        type: 'zip_progress',
        processed: progress.entries.processed,
        total: progress.entries.total,
        percent: Math.round((progress.entries.processed / progress.entries.total) * 100)
      });
    });

    // Handle response errors
    res.on('error', (err) => {
      console.error('📦 [ZIP] Response stream error:', err);
      if (archive) {
        archive.abort();
      }
    });

    // Pipe archive data to response
    archive.pipe(res);

    // Add event listeners for debugging
    archive.on('entry', (entry) => {
      console.log(`📦 [ZIP] Added entry: ${entry.name} (${entry.stats.size} bytes)`);
    });

    archive.on('end', () => {
      console.log('📦 [ZIP] Archive stream ended');
    });

    // Add files to archive
    for (const filename of videoFiles) {
      const filePath = path.join(sessionOutputDir, filename);

      try {
        // Check if file exists and get stats
        const stats = await fs.stat(filePath);
        console.log(`📦 [ZIP] Adding file: ${filename} (${stats.size} bytes)`);

        // Add file to archive with proper options
        archive.file(filePath, {
          name: filename,
          stats: stats // Pass stats to avoid re-reading
        });
      } catch (fileError) {
        console.error(`📦 [ZIP] Error adding file ${filename}:`, fileError);
        // Continue with other files
      }
    }

    // Finalize the archive with proper Promise handling
    console.log(`📦 [ZIP] Finalizing archive with ${videoFiles.length} files`);

    // Use Promise-based approach for finalization
    archive.finalize().then(() => {
      clearTimeout(timeout); // Clear the timeout
      console.log(`📦 [ZIP] ZIP archive finalized successfully: ${zipFilename}`);

      // Send completion notification
      req.app.locals.sendProgress(sessionId, {
        type: 'zip_completed',
        totalFiles: videoFiles.length,
        filename: zipFilename,
        message: `ZIP archive created successfully with ${videoFiles.length} videos`
      });
    }).catch((finalizeError) => {
      clearTimeout(timeout); // Clear the timeout
      console.error('📦 [ZIP] Archive finalization error:', finalizeError);
      if (!res.headersSent) {
        res.status(500).json({ error: 'Failed to finalize ZIP archive' });
      }
    });

  } catch (error) {
    console.error('📦 [ZIP] ZIP creation error:', error);
    if (!res.headersSent) {
      res.status(500).json({
        error: 'Failed to create ZIP archive',
        message: error.message
      });
    }
  }
});

// Download generated video
router.get('/:sessionId/:filename', async (req, res) => {
  try {
    const { sessionId, filename } = req.params;
    
    // Validate session
    const session = req.app.locals.activeSessions.get(sessionId);
    if (!session) {
      return res.status(404).json({ error: 'Session not found' });
    }

    // Construct file path
    const filePath = path.join(req.app.locals.outputDir, sessionId, filename);
    
    // Check if file exists
    if (!await fs.pathExists(filePath)) {
      return res.status(404).json({ error: 'File not found' });
    }

    // Get file stats
    const stats = await fs.stat(filePath);
    
    // Set headers for download
    res.setHeader('Content-Type', 'video/mp4');
    res.setHeader('Content-Length', stats.size);
    res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);
    res.setHeader('Cache-Control', 'no-cache');

    // Stream the file
    const fileStream = fs.createReadStream(filePath);
    
    fileStream.on('error', (error) => {
      console.error('File stream error:', error);
      if (!res.headersSent) {
        res.status(500).json({ error: 'Download failed' });
      }
    });

    fileStream.pipe(res);

    // Log download
    console.log(`Downloaded: ${filename} (${stats.size} bytes)`);

  } catch (error) {
    console.error('Download error:', error);
    if (!res.headersSent) {
      res.status(500).json({ 
        error: 'Download failed',
        message: error.message 
      });
    }
  }
});

// Stream video for preview
router.get('/stream/:sessionId/:filename', async (req, res) => {
  try {
    const { sessionId, filename } = req.params;
    
    // Validate session
    const session = req.app.locals.activeSessions.get(sessionId);
    if (!session) {
      return res.status(404).json({ error: 'Session not found' });
    }

    // Construct file path
    const filePath = path.join(req.app.locals.outputDir, sessionId, filename);
    
    // Check if file exists
    if (!await fs.pathExists(filePath)) {
      return res.status(404).json({ error: 'File not found' });
    }

    // Get file stats
    const stats = await fs.stat(filePath);
    const fileSize = stats.size;

    // Handle range requests for video streaming
    const range = req.headers.range;
    
    if (range) {
      // Parse range header
      const parts = range.replace(/bytes=/, "").split("-");
      const start = parseInt(parts[0], 10);
      const end = parts[1] ? parseInt(parts[1], 10) : fileSize - 1;
      const chunksize = (end - start) + 1;

      // Create read stream for the requested range
      const fileStream = fs.createReadStream(filePath, { start, end });

      // Set partial content headers
      res.writeHead(206, {
        'Content-Range': `bytes ${start}-${end}/${fileSize}`,
        'Accept-Ranges': 'bytes',
        'Content-Length': chunksize,
        'Content-Type': 'video/mp4',
      });

      fileStream.pipe(res);
    } else {
      // No range requested, send entire file
      res.writeHead(200, {
        'Content-Length': fileSize,
        'Content-Type': 'video/mp4',
        'Accept-Ranges': 'bytes'
      });

      const fileStream = fs.createReadStream(filePath);
      fileStream.pipe(res);
    }

  } catch (error) {
    console.error('Stream error:', error);
    if (!res.headersSent) {
      res.status(500).json({ 
        error: 'Stream failed',
        message: error.message 
      });
    }
  }
});

// Delete a generated file
router.delete('/:sessionId/:filename', async (req, res) => {
  try {
    const { sessionId, filename } = req.params;
    
    // Validate session
    const session = req.app.locals.activeSessions.get(sessionId);
    if (!session) {
      return res.status(404).json({ error: 'Session not found' });
    }

    // Construct file path
    const filePath = path.join(req.app.locals.outputDir, sessionId, filename);
    
    // Check if file exists
    if (!await fs.pathExists(filePath)) {
      return res.status(404).json({ error: 'File not found' });
    }

    // Delete the file
    await fs.remove(filePath);

    // Send progress update
    req.app.locals.sendProgress(sessionId, {
      type: 'file_deleted',
      filename: filename
    });

    res.json({ success: true, message: 'File deleted successfully' });

  } catch (error) {
    console.error('Delete file error:', error);
    res.status(500).json({ 
      error: 'Delete failed',
      message: error.message 
    });
  }
});

// Cleanup old files (utility endpoint)
router.post('/cleanup/:sessionId', async (req, res) => {
  try {
    const { sessionId } = req.params;
    const { olderThanHours = 24 } = req.body;
    
    // Validate session
    const session = req.app.locals.activeSessions.get(sessionId);
    if (!session) {
      return res.status(404).json({ error: 'Session not found' });
    }

    const sessionOutputDir = path.join(req.app.locals.outputDir, sessionId);
    
    if (!await fs.pathExists(sessionOutputDir)) {
      return res.json({ deletedFiles: [] });
    }

    const files = await fs.readdir(sessionOutputDir);
    const cutoffTime = new Date(Date.now() - (olderThanHours * 60 * 60 * 1000));
    const deletedFiles = [];

    for (const filename of files) {
      const filePath = path.join(sessionOutputDir, filename);
      const stats = await fs.stat(filePath);
      
      if (stats.birthtime < cutoffTime) {
        await fs.remove(filePath);
        deletedFiles.push(filename);
      }
    }

    res.json({ 
      success: true, 
      deletedFiles,
      message: `Cleaned up ${deletedFiles.length} old files`
    });

  } catch (error) {
    console.error('Cleanup error:', error);
    res.status(500).json({ 
      error: 'Cleanup failed',
      message: error.message 
    });
  }
});

module.exports = router;
