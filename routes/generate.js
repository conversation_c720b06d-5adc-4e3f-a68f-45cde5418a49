const express = require('express');
const path = require('path');
const fs = require('fs-extra');
const ffmpeg = require('fluent-ffmpeg');
const ffmpegStatic = require('ffmpeg-static');
const { v4: uuidv4 } = require('uuid');

const router = express.Router();

// Set FFmpeg path
ffmpeg.setFfmpegPath(ffmpegStatic);

// Generate remixed videos
router.post('/:sessionId', async (req, res) => {
  try {
    const { sessionId } = req.params;
    const {
      clipsPerVideo = 10,
      numberOfVideos = 1,
      useSplitterVideo = false,
      splitterVideoId = null
    } = req.body;

    const session = req.app.locals.activeSessions.get(sessionId);
    if (!session) {
      return res.status(404).json({ error: 'Session not found' });
    }

    if (session.files.length === 0) {
      return res.status(400).json({ error: 'No files uploaded' });
    }

    // Validate parameters
    if (clipsPerVideo < 2 || clipsPerVideo > 50) {
      return res.status(400).json({ error: 'Clips per video must be between 2 and 50' });
    }

    if (numberOfVideos < 1 || numberOfVideos > 100) {
      return res.status(400).json({ error: 'Number of videos must be between 1 and 100' });
    }

    // Validate splitter video if requested
    if (useSplitterVideo && (!splitterVideoId || !session.splitterVideo)) {
      return res.status(400).json({ error: 'Splitter video required when splitter option is enabled' });
    }

    session.status = 'generating';
    
    // Send initial progress
    req.app.locals.sendProgress(sessionId, {
      type: 'generation_started',
      totalVideos: numberOfVideos,
      clipsPerVideo: clipsPerVideo
    });

    // Create output directory for this session
    const sessionOutputDir = path.join(req.app.locals.outputDir, sessionId);
    await fs.ensureDir(sessionOutputDir);

    // Start generation process (don't await - let it run in background)
    generateVideos(sessionId, {
      files: session.files,
      clipsPerVideo,
      numberOfVideos,
      useSplitterVideo,
      splitterVideo: session.splitterVideo,
      outputDir: sessionOutputDir,
      sendProgress: req.app.locals.sendProgress
    }).catch(error => {
      console.error('Generation error:', error);
      req.app.locals.sendProgress(sessionId, {
        type: 'error',
        message: error.message
      });
    });

    res.json({ 
      success: true,
      message: 'Generation started',
      sessionId
    });

  } catch (error) {
    console.error('Generate route error:', error);
    res.status(500).json({ 
      error: 'Generation failed to start',
      message: error.message 
    });
  }
});

// Main video generation function
async function generateVideos(sessionId, options) {
  const {
    files,
    clipsPerVideo,
    numberOfVideos,
    useSplitterVideo,
    splitterVideo,
    outputDir,
    sendProgress
  } = options;

  try {
    for (let videoIndex = 0; videoIndex < numberOfVideos; videoIndex++) {
      sendProgress(sessionId, {
        type: 'video_started',
        videoIndex: videoIndex + 1,
        totalVideos: numberOfVideos
      });

      // Generate random clips for this video
      const clips = await generateRandomClips(files, clipsPerVideo);

      // Create video filename
      const outputFilename = `remix_${videoIndex + 1}_${Date.now()}.mp4`;
      const outputPath = path.join(outputDir, outputFilename);

      // Process video with clips
      await processVideoWithClips(clips, outputPath, useSplitterVideo, splitterVideo, (progress) => {
        sendProgress(sessionId, {
          type: 'video_progress',
          videoIndex: videoIndex + 1,
          progress: progress,
          filename: outputFilename
        });
      });

      // Verify file was actually created
      const fileExists = await fs.pathExists(outputPath);
      const fileStats = fileExists ? await fs.stat(outputPath) : null;

      console.log(`🎬 [GENERATION] Video ${videoIndex + 1} completed:`, {
        filename: outputFilename,
        path: outputPath,
        exists: fileExists,
        size: fileStats?.size || 0,
        sessionId: sessionId
      });

      // Video completed
      sendProgress(sessionId, {
        type: 'video_completed',
        videoIndex: videoIndex + 1,
        filename: outputFilename,
        downloadUrl: `/api/download/${sessionId}/${outputFilename}`,
        fileExists: fileExists,
        fileSize: fileStats?.size || 0
      });
    }

    // List all files in output directory for verification
    try {
      const outputDirContents = await fs.readdir(outputDir);
      console.log(`📁 [GENERATION] Final output directory contents for session ${sessionId}:`, outputDirContents);
    } catch (dirError) {
      console.error(`❌ [GENERATION] Error reading output directory for session ${sessionId}:`, dirError);
    }

    // All videos completed
    sendProgress(sessionId, {
      type: 'generation_completed',
      totalVideos: numberOfVideos
    });

    console.log(`🎉 [GENERATION] All ${numberOfVideos} videos completed for session ${sessionId}`);
    console.log(`📂 [GENERATION] Output directory: ${outputDir}`);

  } catch (error) {
    console.error('Video generation error:', error);
    sendProgress(sessionId, {
      type: 'error',
      message: error.message
    });
  }
}

// Generate random clips using Fisher-Yates shuffle
async function generateRandomClips(files, clipsPerVideo) {
  const clips = [];

  for (let i = 0; i < clipsPerVideo; i++) {
    // Fisher-Yates shuffle to select random file
    const randomFileIndex = Math.floor(Math.random() * files.length);
    const selectedFile = files[randomFileIndex];

    // Use random portion of the video (from 0.1 seconds to full duration)
    const minDuration = 0.1; // Minimum 0.1 seconds
    const maxDuration = selectedFile.duration || 10; // Use full duration or default to 10s

    // Generate random clip duration (can be anywhere from 0.1s to full video)
    const clipDuration = Math.random() * (maxDuration - minDuration) + minDuration;

    // Generate random start time (ensure clip doesn't exceed video duration)
    const maxStartTime = Math.max(0, maxDuration - clipDuration);
    const startTime = Math.random() * maxStartTime;

    clips.push({
      filePath: selectedFile.path,
      startTime: startTime,
      duration: Math.min(clipDuration, maxDuration - startTime),
      originalName: selectedFile.originalName
    });
  }

  return clips;
}

// Process video with clips using FFmpeg
function processVideoWithClips(clips, outputPath, useSplitterVideo, splitterVideo, progressCallback) {
  return new Promise((resolve, reject) => {
    const command = ffmpeg();
    let filterComplex = '';
    let inputIndex = 0;

    // Add all clip inputs
    clips.forEach((clip, index) => {
      command.input(clip.filePath)
        .inputOptions([`-ss ${clip.startTime}`, `-t ${clip.duration}`]);

      // Scale and pad each clip
      filterComplex += `[${inputIndex}:v]scale=1280:720:force_original_aspect_ratio=decrease,pad=1280:720:(ow-iw)/2:(oh-ih)/2,setsar=1[v${index}];`;
      filterComplex += `[${inputIndex}:a]aresample=44100[a${index}];`;
      inputIndex++;
    });

    // Add splitter video inputs if needed
    let splitterInputs = [];
    if (useSplitterVideo && splitterVideo && clips.length > 1) {
      // Add splitter video input for each gap between clips
      for (let i = 0; i < clips.length - 1; i++) {
        command.input(splitterVideo.path);
        filterComplex += `[${inputIndex}:v]scale=1280:720:force_original_aspect_ratio=decrease,pad=1280:720:(ow-iw)/2:(oh-ih)/2,setsar=1[s${i}v];`;
        filterComplex += `[${inputIndex}:a]aresample=44100[s${i}a];`;
        splitterInputs.push({ video: `s${i}v`, audio: `s${i}a` });
        inputIndex++;
      }
    }

    // Build concatenation filter
    let concatInputs = [];
    for (let i = 0; i < clips.length; i++) {
      concatInputs.push(`[v${i}][a${i}]`);

      // Add splitter between clips (except after the last clip)
      if (i < clips.length - 1 && splitterInputs[i]) {
        concatInputs.push(`[${splitterInputs[i].video}][${splitterInputs[i].audio}]`);
      }
    }

    const totalSegments = concatInputs.length;
    filterComplex += concatInputs.join('') + `concat=n=${totalSegments}:v=1:a=1[outv][outa]`;

    console.log('FFmpeg filter complex:', filterComplex);

    command
      .complexFilter(filterComplex)
      .outputOptions([
        '-map [outv]',
        '-map [outa]',
        '-c:v libx264',
        '-c:a aac',
        '-preset fast',
        '-crf 23'
      ])
      .output(outputPath)
      .on('start', (commandLine) => {
        console.log(`🎬 [FFMPEG] Starting video processing: ${outputPath}`);
        console.log(`🎬 [FFMPEG] Command: ${commandLine}`);
      })
      .on('progress', (progress) => {
        if (progressCallback) {
          progressCallback(progress.percent || 0);
        }
      })
      .on('end', async () => {
        console.log(`✅ [FFMPEG] Video processing completed: ${outputPath}`);

        // Verify file was created
        try {
          const fileExists = await fs.pathExists(outputPath);
          const fileStats = fileExists ? await fs.stat(outputPath) : null;
          console.log(`📄 [FFMPEG] Output file verification:`, {
            path: outputPath,
            exists: fileExists,
            size: fileStats?.size || 0
          });
        } catch (verifyError) {
          console.error(`❌ [FFMPEG] Error verifying output file:`, verifyError);
        }

        resolve();
      })
      .on('error', (err) => {
        console.error(`❌ [FFMPEG] Processing error for ${outputPath}:`, err);
        reject(err);
      })
      .run();
  });
}

// Get generation status
router.get('/status/:sessionId', (req, res) => {
  const { sessionId } = req.params;
  const session = req.app.locals.activeSessions.get(sessionId);
  
  if (!session) {
    return res.status(404).json({ error: 'Session not found' });
  }

  res.json({
    status: session.status,
    files: session.files.length,
    created: session.created
  });
});

module.exports = router;
