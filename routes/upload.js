const express = require('express');
const multer = require('multer');
const path = require('path');
const fs = require('fs-extra');
const ffmpeg = require('fluent-ffmpeg');
const ffmpegStatic = require('ffmpeg-static');

const router = express.Router();

// Set FFmpeg path with error handling
try {
  ffmpeg.setFfmpegPath(ffmpegStatic);
  console.log('FFmpeg path set successfully');
} catch (error) {
  console.warn('FFmpeg setup warning:', error.message);
}

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    const sessionId = req.body.sessionId || req.headers['x-session-id'];
    const uploadPath = path.join(req.app.locals.uploadsDir, sessionId);
    fs.ensureDirSync(uploadPath);
    cb(null, uploadPath);
  },
  filename: (req, file, cb) => {
    // Keep original filename but ensure it's safe
    const safeName = file.originalname.replace(/[^a-zA-Z0-9.-]/g, '_');
    cb(null, safeName);
  }
});

const fileFilter = (req, file, cb) => {
  // Check if file is a supported video format
  const allowedMimes = [
    'video/mp4',
    'video/avi',
    'video/quicktime', // .mov
    'video/x-msvideo', // .avi
    'video/x-matroska', // .mkv
    'video/webm'
  ];
  
  const allowedExts = ['.mp4', '.avi', '.mov', '.mkv', '.webm'];
  const fileExt = path.extname(file.originalname).toLowerCase();
  
  if (allowedMimes.includes(file.mimetype) || allowedExts.includes(fileExt)) {
    cb(null, true);
  } else {
    cb(new Error(`Unsupported file format: ${file.originalname}. Supported formats: MP4, AVI, MOV, MKV, WebM`), false);
  }
};

const upload = multer({
  storage,
  fileFilter,
  limits: {
    fileSize: 500 * 1024 * 1024, // 500MB limit per file
    files: 50, // Allow up to 50 files per request (increased from 10)
    fields: 10, // Limit form fields
    parts: 60 // Total parts (files + fields)
  }
});

// Upload single file
router.post('/file', (req, res) => {
  upload.single('video')(req, res, async (err) => {
    try {
      // Handle multer errors
      if (err) {
        console.error('Multer error:', err);
        return res.status(400).json({
          error: 'File upload error',
          message: err.message
        });
      }

      const sessionId = req.body.sessionId || req.headers['x-session-id'];

      if (!sessionId) {
        return res.status(400).json({ error: 'Session ID required' });
      }

      if (!req.file) {
        return res.status(400).json({ error: 'No file uploaded' });
      }

      const session = req.app.locals.activeSessions.get(sessionId);
      if (!session) {
        return res.status(404).json({ error: 'Session not found' });
      }

      // Get video metadata (with fallback if FFmpeg fails)
      let videoInfo;
      try {
        videoInfo = await getVideoInfo(req.file.path);
      } catch (ffmpegError) {
        console.warn('FFmpeg probe failed, using defaults:', ffmpegError.message);
        videoInfo = {
          duration: 0,
          width: 0,
          height: 0,
          bitrate: 0,
          format: 'unknown'
        };
      }

      const fileData = {
        id: Date.now().toString(),
        originalName: req.file.originalname,
        filename: req.file.filename,
        path: req.file.path,
        size: req.file.size,
        duration: videoInfo.duration,
        uploaded: new Date()
      };

      session.files.push(fileData);

      // Send progress update
      req.app.locals.sendProgress(sessionId, {
        type: 'file_uploaded',
        file: {
          id: fileData.id,
          name: fileData.originalName,
          size: fileData.size,
          duration: fileData.duration
        },
        totalFiles: session.files.length
      });

      res.json({
        success: true,
        file: {
          id: fileData.id,
          name: fileData.originalName,
          size: fileData.size,
          duration: fileData.duration
        }
      });

    } catch (error) {
      console.error('Upload error:', error);
      res.status(500).json({
        error: 'Upload failed',
        message: error.message
      });
    }
  });
});

// Upload multiple files
router.post('/files', (req, res) => {
  upload.array('videos', 50)(req, res, async (err) => {
    try {
      // Handle multer errors
      if (err) {
        console.error('Multer error:', err);

        // Handle specific multer error codes
        if (err.code === 'LIMIT_FILE_COUNT') {
          return res.status(400).json({
            error: 'Too many files',
            message: `Maximum 50 files allowed per batch. You tried to upload too many files. Please upload in smaller batches.`
          });
        }

        if (err.code === 'LIMIT_FILE_SIZE') {
          return res.status(400).json({
            error: 'File too large',
            message: 'Maximum file size is 500MB per file.'
          });
        }

        if (err.code === 'LIMIT_UNEXPECTED_FILE') {
          return res.status(400).json({
            error: 'Unexpected file field',
            message: 'Invalid file field name. Use "videos" as the field name.'
          });
        }

        return res.status(400).json({
          error: 'File upload error',
          message: err.message
        });
      }

      const sessionId = req.body.sessionId || req.headers['x-session-id'];

      if (!sessionId) {
        return res.status(400).json({ error: 'Session ID required' });
      }

      if (!req.files || req.files.length === 0) {
        return res.status(400).json({ error: 'No files uploaded' });
      }

      const session = req.app.locals.activeSessions.get(sessionId);
      if (!session) {
        return res.status(404).json({ error: 'Session not found' });
      }

      const uploadedFiles = [];
      console.log(`📦 [UPLOAD] Processing ${req.files.length} files for session ${sessionId}`);

      // Process files one by one to prevent memory overload
      for (let i = 0; i < req.files.length; i++) {
        const file = req.files[i];
        console.log(`📄 [UPLOAD] Processing file ${i + 1}/${req.files.length}: ${file.originalname}`);
        try {
          // Get video metadata (with fallback if FFmpeg fails)
          let videoInfo;
          try {
            console.log(`🔍 [UPLOAD] Probing video metadata for: ${file.originalname}`);
            videoInfo = await getVideoInfo(file.path);
            console.log(`✅ [UPLOAD] Video metadata obtained for: ${file.originalname}`, {
              duration: videoInfo.duration,
              format: videoInfo.format
            });
          } catch (ffmpegError) {
            console.warn(`⚠️  SKIPPING FILE: ${file.originalname} - FFmpeg probe failed:`, ffmpegError.message);
            console.warn(`File will be skipped and not included in processing.`);

            // Delete the problematic file
            try {
              await fs.remove(file.path);
            } catch (deleteError) {
              console.error(`Failed to delete problematic file ${file.originalname}:`, deleteError.message);
            }

            // Skip this file and continue with others
            continue;
          }

          const fileData = {
            id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
            originalName: file.originalname,
            filename: file.filename,
            path: file.path,
            size: file.size,
            duration: videoInfo.duration,
            uploaded: new Date()
          };

          session.files.push(fileData);
          uploadedFiles.push({
            id: fileData.id,
            name: fileData.originalName,
            size: fileData.size,
            duration: fileData.duration
          });

          console.log(`✅ Successfully processed: ${file.originalname} (${videoInfo.duration}s)`);

          // Send progress update for each file
          req.app.locals.sendProgress(sessionId, {
            type: 'file_uploaded',
            file: {
              id: fileData.id,
              name: fileData.originalName,
              size: fileData.size,
              duration: fileData.duration
            },
            totalFiles: session.files.length
          });

        } catch (error) {
          console.error(`❌ SKIPPING FILE: ${file.originalname} - Processing error:`, error.message);

          // Try to delete the problematic file
          try {
            await fs.remove(file.path);
          } catch (deleteError) {
            console.error(`Failed to delete problematic file ${file.originalname}:`, deleteError.message);
          }

          // Continue with other files
        }
      }

      res.json({
        success: true,
        files: uploadedFiles,
        totalFiles: session.files.length
      });

    } catch (error) {
      console.error('Batch upload error:', error);
      res.status(500).json({
        error: 'Batch upload failed',
        message: error.message
      });
    }
  });
});

// Get video information using FFmpeg
function getVideoInfo(filePath) {
  return new Promise((resolve, reject) => {
    ffmpeg.ffprobe(filePath, (err, metadata) => {
      if (err) {
        reject(err);
        return;
      }

      const videoStream = metadata.streams.find(stream => stream.codec_type === 'video');
      
      resolve({
        duration: metadata.format.duration || 0,
        width: videoStream?.width || 0,
        height: videoStream?.height || 0,
        bitrate: metadata.format.bit_rate || 0,
        format: metadata.format.format_name || 'unknown'
      });
    });
  });
}

// Get session files
router.get('/files/:sessionId', (req, res) => {
  const { sessionId } = req.params;
  const session = req.app.locals.activeSessions.get(sessionId);
  
  if (!session) {
    return res.status(404).json({ error: 'Session not found' });
  }

  const files = session.files.map(file => ({
    id: file.id,
    name: file.originalName,
    size: file.size,
    duration: file.duration,
    uploaded: file.uploaded
  }));

  res.json({ files });
});

// Delete uploaded file
router.delete('/file/:sessionId/:fileId', async (req, res) => {
  try {
    const { sessionId, fileId } = req.params;
    const session = req.app.locals.activeSessions.get(sessionId);
    
    if (!session) {
      return res.status(404).json({ error: 'Session not found' });
    }

    const fileIndex = session.files.findIndex(f => f.id === fileId);
    if (fileIndex === -1) {
      return res.status(404).json({ error: 'File not found' });
    }

    const file = session.files[fileIndex];
    
    // Delete physical file
    await fs.remove(file.path);
    
    // Remove from session
    session.files.splice(fileIndex, 1);

    // Send progress update
    req.app.locals.sendProgress(sessionId, {
      type: 'file_deleted',
      fileId: fileId,
      totalFiles: session.files.length
    });

    res.json({ success: true });

  } catch (error) {
    console.error('Delete file error:', error);
    res.status(500).json({ 
      error: 'Delete failed',
      message: error.message 
    });
  }
});

// Upload splitter video
router.post('/splitter', (req, res) => {
  upload.single('video')(req, res, async (err) => {
    try {
      // Handle multer errors
      if (err) {
        console.error('Multer error:', err);
        return res.status(400).json({
          error: 'File upload error',
          message: err.message
        });
      }

      const sessionId = req.body.sessionId || req.headers['x-session-id'];

      if (!sessionId) {
        return res.status(400).json({ error: 'Session ID required' });
      }

      if (!req.file) {
        return res.status(400).json({ error: 'No splitter video uploaded' });
      }

      const session = req.app.locals.activeSessions.get(sessionId);
      if (!session) {
        return res.status(404).json({ error: 'Session not found' });
      }

      // Get video metadata (with fallback if FFmpeg fails)
      let videoInfo;
      try {
        videoInfo = await getVideoInfo(req.file.path);
      } catch (ffmpegError) {
        console.warn('FFmpeg probe failed for splitter video, using defaults:', ffmpegError.message);
        videoInfo = {
          duration: 0,
          width: 0,
          height: 0,
          bitrate: 0,
          format: 'unknown'
        };
      }

      const splitterData = {
        id: 'splitter_' + Date.now().toString(),
        originalName: req.file.originalname,
        filename: req.file.filename,
        path: req.file.path,
        size: req.file.size,
        duration: videoInfo.duration,
        uploaded: new Date(),
        type: 'splitter'
      };

      // Store splitter video in session
      session.splitterVideo = splitterData;

      res.json({
        success: true,
        file: {
          id: splitterData.id,
          name: splitterData.originalName,
          size: splitterData.size,
          duration: splitterData.duration
        }
      });

    } catch (error) {
      console.error('Splitter upload error:', error);
      res.status(500).json({
        error: 'Splitter upload failed',
        message: error.message
      });
    }
  });
});

module.exports = router;
