const express = require('express');
const cors = require('cors');
const path = require('path');
const fs = require('fs-extra');
const { v4: uuidv4 } = require('uuid');

const app = express();
const PORT = process.env.PORT || 3000;

// Increase timeout for large file uploads
app.use((req, res, next) => {
  // Set timeout to 10 minutes for upload routes
  if (req.path.includes('/upload')) {
    req.setTimeout(600000); // 10 minutes
    res.setTimeout(600000); // 10 minutes
  }
  next();
});

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.static('public'));

// Create necessary directories
const uploadsDir = path.join(__dirname, 'uploads');
const outputDir = path.join(__dirname, 'output');
fs.ensureDirSync(uploadsDir);
fs.ensureDirSync(outputDir);

// Store active sessions
const activeSessions = new Map();

// Utility function to send progress updates
function sendProgress(sessionId, data) {
  const session = activeSessions.get(sessionId);
  if (session && session.progressStream) {
    session.progressStream.write(`data: ${JSON.stringify(data)}\n\n`);
  }
}

// Make utility functions available to routes BEFORE loading routes
app.locals.activeSessions = activeSessions;
app.locals.sendProgress = sendProgress;
app.locals.uploadsDir = uploadsDir;
app.locals.outputDir = outputDir;

// Routes
app.use('/api/upload', require('./routes/upload'));
app.use('/api/generate', require('./routes/generate'));
app.use('/api/download', require('./routes/download'));

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    activeSessions: activeSessions.size
  });
});

// Debug endpoint to check FFmpeg
app.get('/debug/ffmpeg', (req, res) => {
  const ffmpeg = require('fluent-ffmpeg');
  const ffmpegStatic = require('ffmpeg-static');

  try {
    ffmpeg.setFfmpegPath(ffmpegStatic);

    // Test FFmpeg by getting version
    ffmpeg.getAvailableFormats((err, formats) => {
      if (err) {
        res.json({
          ffmpeg: 'error',
          error: err.message,
          path: ffmpegStatic
        });
      } else {
        res.json({
          ffmpeg: 'working',
          path: ffmpegStatic,
          formatsCount: Object.keys(formats).length
        });
      }
    });
  } catch (error) {
    res.json({
      ffmpeg: 'setup_error',
      error: error.message,
      path: ffmpegStatic
    });
  }
});

// Debug endpoint to check output directory
app.get('/debug/output/:sessionId?', async (req, res) => {
  try {
    const { sessionId } = req.params;

    if (sessionId) {
      // Check specific session output
      const sessionOutputDir = path.join(outputDir, sessionId);
      const dirExists = await fs.pathExists(sessionOutputDir);

      if (!dirExists) {
        return res.json({
          sessionId,
          outputDir: sessionOutputDir,
          exists: false,
          files: []
        });
      }

      const files = await fs.readdir(sessionOutputDir);
      const fileDetails = await Promise.all(
        files.map(async (filename) => {
          const filePath = path.join(sessionOutputDir, filename);
          const stats = await fs.stat(filePath);
          return {
            filename,
            size: stats.size,
            created: stats.birthtime,
            modified: stats.mtime
          };
        })
      );

      res.json({
        sessionId,
        outputDir: sessionOutputDir,
        exists: true,
        files: fileDetails,
        totalFiles: files.length
      });
    } else {
      // Check all sessions
      const outputDirExists = await fs.pathExists(outputDir);
      if (!outputDirExists) {
        return res.json({
          outputDir,
          exists: false,
          sessions: []
        });
      }

      const sessionDirs = await fs.readdir(outputDir);
      const sessionDetails = await Promise.all(
        sessionDirs.map(async (sessionDir) => {
          const sessionPath = path.join(outputDir, sessionDir);
          const isDir = (await fs.stat(sessionPath)).isDirectory();

          if (!isDir) return null;

          const files = await fs.readdir(sessionPath);
          return {
            sessionId: sessionDir,
            path: sessionPath,
            fileCount: files.length,
            files: files
          };
        })
      );

      res.json({
        outputDir,
        exists: true,
        sessions: sessionDetails.filter(s => s !== null),
        activeSessions: Array.from(activeSessions.keys())
      });
    }
  } catch (error) {
    res.json({
      error: error.message,
      outputDir: outputDir
    });
  }
});

// Session management
app.post('/api/session', (req, res) => {
  const sessionId = uuidv4();
  const sessionData = {
    id: sessionId,
    created: new Date(),
    files: [],
    status: 'created'
  };
  activeSessions.set(sessionId, sessionData);
  console.log(`🔑 [SESSION] Created session: ${sessionId}`);
  console.log(`📊 [SESSION] Total active sessions: ${activeSessions.size}`);
  res.json({ sessionId });
});

// Debug endpoint for sessions
app.get('/debug/sessions', (req, res) => {
  const sessions = Array.from(activeSessions.entries()).map(([id, data]) => ({
    id,
    created: data.created,
    filesCount: data.files?.length || 0,
    status: data.status
  }));

  res.json({
    totalSessions: activeSessions.size,
    sessions: sessions
  });
});

app.delete('/api/session/:sessionId', async (req, res) => {
  const { sessionId } = req.params;
  const session = activeSessions.get(sessionId);
  
  if (session) {
    // Cleanup files
    try {
      const sessionUploadDir = path.join(uploadsDir, sessionId);
      const sessionOutputDir = path.join(outputDir, sessionId);
      
      await fs.remove(sessionUploadDir);
      await fs.remove(sessionOutputDir);
    } catch (error) {
      console.error('Cleanup error:', error);
    }
    
    activeSessions.delete(sessionId);
  }
  
  res.json({ success: true });
});

// Server-Sent Events endpoint for progress updates
app.get('/api/progress/:sessionId', (req, res) => {
  const { sessionId } = req.params;
  
  res.writeHead(200, {
    'Content-Type': 'text/event-stream',
    'Cache-Control': 'no-cache',
    'Connection': 'keep-alive',
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'Cache-Control'
  });

  const session = activeSessions.get(sessionId);
  if (!session) {
    res.write(`data: ${JSON.stringify({ error: 'Session not found' })}\n\n`);
    res.end();
    return;
  }

  // Store the response object for this session
  session.progressStream = res;

  // Send initial status
  res.write(`data: ${JSON.stringify({ 
    type: 'status', 
    status: session.status,
    message: 'Connected to progress stream'
  })}\n\n`);

  // Handle client disconnect
  req.on('close', () => {
    if (session.progressStream === res) {
      delete session.progressStream;
    }
  });
});



// Error handling middleware
app.use((error, req, res, next) => {
  console.error('Server error:', error);

  // Handle multer errors specifically
  if (error.code === 'LIMIT_FILE_SIZE') {
    return res.status(400).json({
      error: 'File too large',
      message: 'File size exceeds the 500MB limit'
    });
  }

  if (error.code === 'LIMIT_FILE_COUNT') {
    return res.status(400).json({
      error: 'Too many files',
      message: 'Maximum 50 files allowed'
    });
  }

  if (error.code === 'LIMIT_UNEXPECTED_FILE') {
    return res.status(400).json({
      error: 'Unexpected file field',
      message: 'Invalid file field name'
    });
  }

  // Handle other multer errors
  if (error.message && error.message.includes('Unsupported file format')) {
    return res.status(400).json({
      error: 'Unsupported file format',
      message: error.message
    });
  }

  // Generic error response
  res.status(500).json({
    error: 'Internal server error',
    message: error.message
  });
});

// Start server
app.listen(PORT, () => {
  console.log(`LazyRemixer server running on port ${PORT}`);
  console.log(`Health check: http://localhost:${PORT}/health`);
});

// Graceful shutdown
process.on('SIGTERM', async () => {
  console.log('Shutting down gracefully...');
  
  // Close all progress streams
  for (const session of activeSessions.values()) {
    if (session.progressStream) {
      session.progressStream.end();
    }
  }
  
  process.exit(0);
});
