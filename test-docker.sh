#!/bin/bash

# LazyRemixer Docker Test Script

echo "🎬 LazyRemixer Docker Test"
echo "=========================="

# Check if Docker is available
if ! command -v docker &> /dev/null; then
    echo "❌ Docker is not installed or not in PATH"
    exit 1
fi

echo "✅ Docker is available"

# Check if docker-compose is available
if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose is not installed or not in PATH"
    exit 1
fi

echo "✅ Docker Compose is available"

# Build the Docker image
echo "🔨 Building Docker image..."
docker build -t lazy-remixer . || {
    echo "❌ Failed to build Docker image"
    exit 1
}

echo "✅ Docker image built successfully"

# Test with docker-compose
echo "🚀 Testing with Docker Compose..."
docker-compose up -d || {
    echo "❌ Failed to start with Docker Compose"
    exit 1
}

echo "⏳ Waiting for service to be ready..."
sleep 10

# Test health endpoint
echo "🏥 Testing health endpoint..."
HEALTH_RESPONSE=$(curl -s http://localhost:3000/health)

if [[ $? -eq 0 ]] && [[ $HEALTH_RESPONSE == *"healthy"* ]]; then
    echo "✅ Health check passed"
    echo "Response: $HEALTH_RESPONSE"
else
    echo "❌ Health check failed"
    echo "Response: $HEALTH_RESPONSE"
    docker-compose logs
    docker-compose down
    exit 1
fi

# Test main page
echo "🌐 Testing main page..."
HTTP_STATUS=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:3000/)

if [[ $HTTP_STATUS == "200" ]]; then
    echo "✅ Main page accessible"
else
    echo "❌ Main page not accessible (HTTP $HTTP_STATUS)"
    docker-compose logs
    docker-compose down
    exit 1
fi

echo "🎉 All tests passed!"
echo ""
echo "LazyRemixer is running at: http://localhost:3000"
echo ""
echo "To stop the service, run: docker-compose down"
echo "To view logs, run: docker-compose logs -f"

# Keep running for manual testing
echo "Press Ctrl+C to stop the service and exit"
trap 'echo ""; echo "🛑 Stopping service..."; docker-compose down; exit 0' INT

# Wait for interrupt
while true; do
    sleep 1
done
