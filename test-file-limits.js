// Test script to verify file upload limits
const FormData = require('form-data');
const fetch = require('node-fetch');
const fs = require('fs');

async function testFileUploadLimits() {
  console.log('🧪 Testing File Upload Limits');
  console.log('==============================');

  try {
    // Create a session first
    console.log('1. Creating session...');
    const sessionResponse = await fetch('http://localhost:3000/api/session', {
      method: 'POST'
    });
    const sessionData = await sessionResponse.json();
    const sessionId = sessionData.sessionId;
    console.log(`✅ Session created: ${sessionId}`);

    // Create some dummy files for testing
    console.log('2. Creating test files...');
    const testFiles = [];
    for (let i = 0; i < 15; i++) {
      const filename = `test_video_${i}.mp4`;
      fs.writeFileSync(filename, `Test video content ${i}`);
      testFiles.push(filename);
    }
    console.log(`✅ Created ${testFiles.length} test files`);

    // Test uploading files
    console.log('3. Testing file upload...');
    const formData = new FormData();
    formData.append('sessionId', sessionId);
    
    testFiles.forEach(filename => {
      formData.append('videos', fs.createReadStream(filename));
    });

    const uploadResponse = await fetch('http://localhost:3000/api/upload/files', {
      method: 'POST',
      body: formData
    });

    console.log(`Upload response status: ${uploadResponse.status}`);
    
    if (uploadResponse.ok) {
      const result = await uploadResponse.json();
      console.log(`✅ Upload successful: ${result.files?.length || 0} files processed`);
    } else {
      const error = await uploadResponse.json();
      console.log(`❌ Upload failed: ${error.error} - ${error.message}`);
    }

    // Cleanup test files
    console.log('4. Cleaning up test files...');
    testFiles.forEach(filename => {
      try {
        fs.unlinkSync(filename);
      } catch (e) {
        // Ignore cleanup errors
      }
    });
    console.log('✅ Cleanup completed');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

// Run the test
testFileUploadLimits();
