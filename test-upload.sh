#!/bin/bash

# Test script for LazyRemixer upload functionality

echo "🧪 Testing LazyRemixer Upload Functionality"
echo "=========================================="

# Check if server is running
echo "1. Testing server health..."
HEALTH_RESPONSE=$(curl -s http://localhost:3000/health)
if [[ $? -eq 0 ]]; then
    echo "✅ Server is running"
    echo "Health response: $HEALTH_RESPONSE"
else
    echo "❌ Server is not running"
    exit 1
fi

# Create a session
echo ""
echo "2. Creating session..."
SESSION_RESPONSE=$(curl -s -X POST http://localhost:3000/api/session)
SESSION_ID=$(echo $SESSION_RESPONSE | grep -o '"sessionId":"[^"]*"' | cut -d'"' -f4)

if [[ -n "$SESSION_ID" ]]; then
    echo "✅ Session created: $SESSION_ID"
else
    echo "❌ Failed to create session"
    echo "Response: $SESSION_RESPONSE"
    exit 1
fi

# Test debug endpoints
echo ""
echo "3. Testing debug endpoints..."
echo "Sessions debug:"
curl -s http://localhost:3000/debug/sessions | jq .

echo ""
echo "Output debug:"
curl -s http://localhost:3000/debug/output | jq .

echo ""
echo "Download test:"
curl -s http://localhost:3000/api/download/test | jq .

echo ""
echo "4. Testing download list (should be empty)..."
DOWNLOAD_RESPONSE=$(curl -s http://localhost:3000/api/download/list/$SESSION_ID)
echo "Download list response: $DOWNLOAD_RESPONSE"

echo ""
echo "✅ All tests completed!"
echo "Session ID for manual testing: $SESSION_ID"
echo ""
echo "To test file upload manually:"
echo "1. Go to http://localhost:3000"
echo "2. Use session ID: $SESSION_ID"
echo "3. Try uploading a small batch of files (5 or fewer)"
